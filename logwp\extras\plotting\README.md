# logwp.extras.plotting - 现代化绘图配置服务

## 概述

`logwp.extras.plotting` 是SCAPE项目的现代化绘图配置系统，提供基于配置模板的绘图解决方案。该系统采用"配置即服务"的设计理念，实现了绘图逻辑与样式配置的完全解耦，并支持多种绘图后端（如Matplotlib和Plotly）。

## 🎯 设计理念

### 重构背景与动机

传统的绘图系统存在以下问题：

- **配置僵化**：旧的配置类只能定义固定的绘图属性，无法灵活适应不同图表的特殊需求
- **职责不清**：绘图美学风格与保存格式耦合，上游用户需要处理复杂的参数组合
- **需求错配**：项目核心需求是按"绘图类型"配置，而非传递通用参数
- **可维护性差**：新增图表类型或修改全局风格需要多处侵入式修改
- **用户不友好**：最终用户无法在不修改代码的情况下定制图表外观

### 核心设计原则

#### 1. 配置即服务 (Configuration as a Service)

将绘图系统从简单的"参数传递"模式升级为现代化的"配置即服务"模式：

- **模板化管理**：每种绘图类型（如`capture_curve`）封装为独立、可复用的配置模板 (`PlotProfile` / `PlotStyle`)
- **中心化注册**：`PlottingConfigRegistry`统一管理所有绘图模板，提供默认最佳实践
- **用户可定制**：支持从外部JSON文件加载和保存配置，无需接触代码
- **架构解耦**：上游用户与具体绘图实现完全解耦，只需关心"需要什么类型的图"

#### 2. 两级继承体系 (Two-Level Inheritance System)

为了同时满足"项目内所有图表风格统一"和"特定模块内图表风格统一"的需求，我们为 **`PlotProfile` 和 `PlotStyle`** 提供了相同的两级继承体系：

```
全局根模板 (base)
└── 模块级基础模板 (module.base)
    └── 具体图表模板 (module.chart)
```

**全局根模板 (`base`)**：
- 定义项目级视觉识别标准（官方字体、品牌色板等）
- 是所有样式配置的"最终回退点"，确保基础风格的一致性
- 在`logwp.extras.plotting`包中定义并注册

**模块级基础模板 (`module.base`)**：
- 定义特定功能模块内部所有图表的通用风格
- 继承自全局根模板，可覆盖全局设置
- 在具体模块中定义（如`validation.base`、`swift_pso.base`）

**具体图表模板 (`module.chart`)**：
- 定义特定图表的专有样式和配置
- 继承自对应的模块级基础模板
- 包含图表特有的绘图元素配置

#### 3. 层叠合并逻辑 (Cascade Merging)

当用户请求具体模板时（如`registry.get("validation.contribution_crossplot", expected_type=PlotProfile)`），系统会调用该配置对象的 `resolve()` 方法，执行三级层叠合并：

1. **起始点**：以当前请求的模板实例为基础。
2. **第一层合并**：查找并合并模块级基础模板（`validation.base`）。
3. **第二层合并**：查找并合并全局根模板（`base`）。
4. **返回结果**：返回一个经过完整层叠合并的、全新的配置对象。

**深度合并策略**：对于字典类型属性（如`rc_params`、`artist_props`），采用深度合并而非浅合并，确保配置正确组合。

### 软件设计模式

#### 注册表模式 (Registry Pattern)
- `PlottingConfigRegistry`作为单例，自动收集和分发绘图配置
- 支持运行时注册、查询和持久化操作
- **类型感知**：能够管理同名的不同类型配置（如`PlotProfile`和`PlotStyle`）

#### 数据驱动配置 (Data-Driven Configuration)
- 绘图风格由可序列化的数据对象（`PlotProfile` / `PlotStyle`）驱动
- 配置与代码逻辑完全分离
- 支持JSON格式的外部配置文件

#### 关注点分离 (Separation of Concerns)
- **开发者职责**：定义和注册效果良好的默认`PlotProfile`和`PlotStyle`
- **使用者职责**：通过外部JSON文件定制图表外观
- **系统职责**：提供配置管理、继承合并、样式应用等服务

### 架构优势

- **高内聚低耦合**：绘图配置逻辑高度内聚到`logwp.extras.plotting`包中
- **最大化代码复用**：全局配置只需定义一次，模块级通用配置也只需定义一次
- **分层级一致性**：既保证项目视觉风格统一，也保证特定模块内部图表风格统一
- **极高可维护性**：
  - 修改全局字体只需编辑全局`base`模板
  - 修改模块图表尺寸只需编辑模块级`base`模板
  - 修改单个图表只需编辑其特定模板
- **清晰职责边界**：配置职责清晰划分到全局、模块、具体图表三个层级

## 📁 包结构

```
logwp/extras/plotting/
├── __init__.py          # 公共API导出
├── theme_constants.py   # 全局主题常量 (颜色、字体、线宽)
├── profiles.py          # 核心数据模型 (PlotProfile, SaveConfig)
├── styles.py            # 通用样式模型 (PlotStyle)
├── registry.py          # 配置注册表和继承体系
├── styler.py            # Matplotlib样式应用工具
├── saver.py             # Matplotlib图像保存工具
├── exceptions.py        # 专属异常类
└── README.md            # 本文档
```

## 🚀 快速开始

### 基本使用 (Matplotlib)

```python
import matplotlib.pyplot as plt
from logwp.extras.plotting import registry, apply_profile, save_figure, PlotProfile

# 1. 获取预定义配置，必须指定类型
profile = registry.get("validation.contribution_crossplot", expected_type=PlotProfile)

# 2. 创建图表并应用配置
fig, ax = plt.subplots()
apply_profile(ax, profile)

# 3. 绘制数据
ax.scatter(x_data, y_data, **profile.artist_props.get("scatter", {}))
ax.plot([0, 1], [0, 1], **profile.artist_props.get("reference_line", {}))

# 4. 设置标题和标签
ax.set_title("Contribution Cross-plot")
ax.set_xlabel(profile.label_props.get("xlabel", "X"))
ax.set_ylabel(profile.label_props.get("ylabel", "Y"))

# 5. 保存图表（如果配置了保存设置）
if profile.save_config:
    save_figure(fig, profile.save_config, "output", "crossplot")
```

### 自定义配置

#### 方法1: 从零创建 (适用于完全独立的图表)
```python
from logwp.extras.plotting import PlotProfile, SaveConfig, registry

# 创建自定义配置
custom_profile = PlotProfile(
    name="my_custom_plot",
    figure_props={"figsize": (10, 8)},
    title_props={"fontsize": 16, "fontweight": "bold"},
    artist_props={"scatter": {"s": 80, "alpha": 0.8, "color": "blue"}},
    save_config=SaveConfig(format=["png", "svg"], dpi=600)
)

# 注册自定义配置
registry.register(custom_profile)
```

#### 方法2: 基于模板修改 (推荐)
```python
from logwp.extras.plotting import registry, PlotProfile

# 1. 获取一个基础模板
original = registry.get("validation.contribution_crossplot", expected_type=PlotProfile)

# 2. 使用 .with_updates() 创建一个修改后的副本
updated_profile = original.with_updates(
   title_props={"label": "My Custom Title", "fontsize": 14},
   artist_props={"scatter": {"s": 60, "alpha": 0.7}},
   save_config={"format": "pdf"}
)

# 3. 现在可以使用 updated_profile 进行绘图
# apply_profile(ax, updated_profile)
```

## 🏗️ 核心组件

### PlotProfile - 绘图配置模板 (Matplotlib)

`PlotProfile` 是针对 **Matplotlib** 的绘图配置核心数据模型，采用分层设计理念，将绘图配置分为不同的职责层次：

```python
@dataclass
class PlotProfile:
    name: str                           # 配置名称
    rc_params: Dict[str, Any]           # matplotlib全局参数
    figure_props: Dict[str, Any]        # 图表属性（尺寸、DPI等）
    title_props: Dict[str, Any]         # 标题属性
    label_props: Dict[str, Any]         # 标签属性
    artist_props: Dict[str, Any]        # 绘图元素属性（线条、散点等）
    save_config: Optional[SaveConfig]   # 保存配置
```

### PlotStyle - 通用绘图样式模板 (Plotly)

`PlotStyle` 是一个与具体绘图库无关的通用样式模板，基于Pydantic实现，主要用于 **Plotly** 等现代绘图库。

```python
class PlotStyle(BaseModel):
    name: str                           # 样式名称
    config_type: str = "PlotStyle"      # 类型标识
    font: Dict[str, Any]                # 字体配置
    color: Dict[str, Any]               # 颜色配置
    linewidth: Dict[str, Any]           # 线宽配置
    tick: Dict[str, Any]                # 刻度线配置
    frame: Dict[str, Any]               # 图表边框配置
```


#### 属性详细说明

**`rc_params`** (PlotProfile) - 全局样式主题
- 存放直接传递给`matplotlib.rcParams`的配置参数
- 负责设定全局样式和默认值，就像图表的"皮肤"或"主题"

**`figure_props`** (PlotProfile) - 画布规格设置
- 存放直接传递给`matplotlib.pyplot.figure()`的关键字参数
- 控制整个图表画布（Figure）级别的属性

**`title_props`** (PlotProfile) - 标题精细控制
- 存放传递给`matplotlib.axes.Axes.set_title()`的关键字参数

**`label_props`** (PlotProfile) - 坐标轴标签配置
- 存放传递给`set_xlabel()`和`set_ylabel()`的关键字参数

**`artist_props`** (PlotProfile) - 数据绘制核心
- 存放传递给具体绘图函数（`ax.plot()`、`ax.scatter()`等）的关键字参数

**`save_config`** (PlotProfile) - 保存配置
- 可选的图像保存配置，控制输出格式和质量

### PlottingConfigRegistry - 配置注册表

`PlottingConfigRegistry` 管理所有绘图配置模板，支持两级继承体系，并且能够区分不同类型的配置：

```
全局根模板 (base)
└── 模块级基础模板 (module.base)
    └── 具体图表模板 (module.chart)
```

#### 主要方法

```python
# 注册基础模板
registry.register_base(base_profile)
registry.register_base(base_style)

# 注册具体配置
registry.register(chart_profile)

# 获取配置（自动继承合并，需指定类型）
profile = registry.get("validation.contribution_crossplot", expected_type=PlotProfile)
style = registry.get("petroplot.crossplot.default", expected_type=PlotStyle)

# 列出所有配置
all_profiles = registry.list_profiles(expected_type=PlotProfile)
all_styles = registry.list_profiles(expected_type=PlotStyle)

# 从目录加载配置
registry.load_from_dir("config/plot_profiles")

# 保存所有配置到目录
registry.save_to_dir("config/plot_profiles")
```

### SaveConfig - 保存配置

```python
@dataclass
class SaveConfig:
    format: Union[str, List[str]]       # 保存格式
    dpi: Optional[int] = None           # 分辨率
    width: Optional[float] = None       # 宽度（英寸）
    height: Optional[float] = None      # 高度（英寸）
    transparent: bool = False           # 透明背景
    bbox_inches: str = "tight"          # 边界框设置
    save_kwargs: Dict[str, Any] = None  # 其他保存参数
```

## 📋 配置模板示例

### PlotProfile 基础模板 (Matplotlib)

```json
{
  "name": "validation.base",
  "config_type": "PlotProfile",
  "rc_params": {
    "font.family": "Arial",
    "font.size": 11,
    "axes.grid": true,
    "grid.alpha": 0.3
  },
  "figure_props": {
    "figsize": [7, 7],
    "dpi": 150,
    "layout": "constrained"
  }
}
```

### PlotStyle 基础模板 (Plotly)

```json
{
  "name": "base",
  "config_type": "PlotStyle",
  "font": {
    "family": "Arial",
    "color": "#333333",
    "size_title": 14.0,
    "size_label": 12.0,
    "size_ticks": 10.0
  },
  "color": {
    "cycle": [
      "#1f77b4",
      "#ff7f0e",
      "#2ca02c"
    ],
    "background_plot": "#ffffff",
    "background_canvas": "#ffffff",
    "grid_major": "#bbbbbb"
  },
  "frame": {"color": "#666666", "width": 0.8}
}
```

## 🔧 工具函数

### apply_profile - 样式应用 (Matplotlib)

```python
def apply_profile(ax: plt.Axes, profile: PlotProfile) -> None:
    """将PlotProfile配置应用到matplotlib Axes对象"""
```

### save_figure - 图像保存 (Matplotlib)

```python
def save_figure(
    figure: plt.Figure,
    save_config: SaveConfig,
    base_path: Union[str, Path],
    base_name: str
) -> List[Path]:
    """根据SaveConfig配置保存图像"""
```

## 🛠️ 高级用法

### 配置文件管理

#### 导出所有配置

```bash
# 使用导出脚本
python scripts/plotting/export_profiles.py

# 查看导出的配置
ls config/plot_profiles/
# base.PlotProfile.json
# base.PlotStyle.json
# validation.contribution_crossplot.PlotProfile.json
# petroplot.crossplot.default.PlotStyle.json
# ...
```

#### 自定义配置工作流

```python
# 1. 复制现有配置
import shutil
src_path = "config/plot_profiles/validation.contribution_crossplot.PlotProfile.json"
dst_path = "config/plot_profiles/my_custom_crossplot.PlotProfile.json"
shutil.copy(src_path, dst_path)

# 2. 编辑JSON文件（修改颜色、尺寸等）
# 3. 在代码中加载自定义配置
from logwp.extras.plotting import PlotProfile, registry

custom_profile = PlotProfile.from_json(
    "config/plot_profiles/my_custom_crossplot.PlotProfile.json"
)
registry.register(custom_profile)

# 4. 使用自定义配置
profile = registry.get("my_custom_crossplot", expected_type=PlotProfile)
```

## 🔍 故障排除

### 常见问题

#### Q: 配置未找到错误
```python
# 错误：ProfileNotFoundError: Profile 'my_profile' (类型: PlotProfile) not found
# 解决：检查配置是否已注册
print(registry.list_profiles(expected_type=PlotProfile))  # 查看所有可用配置
```

#### Q: 样式应用失败 (Matplotlib)
```python
# 错误：StyleApplicationError: Failed to apply style
# 解决：检查matplotlib版本兼容性和配置参数有效性
import matplotlib
print(matplotlib.__version__)  # 确保版本 >= 3.5
```

#### Q: JSON序列化错误
```python
# 错误：JSON serialization failed
# 解决：确保配置中的所有值都是JSON可序列化的
# 避免使用numpy数组、复杂对象等
```

### 调试技巧

```python
# 启用详细日志
import logging
logging.getLogger("logwp.extras.plotting").setLevel(logging.DEBUG)

# 检查配置合并结果
profile = registry.get("validation.contribution_crossplot", expected_type=PlotProfile)
print(f"合并后的配置: {profile}")
```

## 🔮 未来规划

### 计划中的功能

1. **主题系统**：支持深色主题、高对比度主题等
2. **动态配置**：支持运行时动态修改配置
3. **配置验证器**：更强的配置有效性检查
4. **可视化编辑器**：图形化配置编辑工具
5. **模板市场**：共享和下载配置模板

### 版本兼容性

- **当前版本**: v1.1 (稳定版)
- **向后兼容**: 保证配置文件格式向后兼容
- **升级路径**: 提供自动迁移工具

## 🤝 贡献指南

### 新增图表类型

1. 在对应模块创建`*_plot_profiles.py`文件
2. 定义配置常量类和注册函数
3. 遵循命名约定：`模块名.图表类型`
4. 提供完整的配置示例和使用文档
5. 确保配置的JSON序列化兼容性

### 代码贡献

1. 遵循SCAPE编码规范
2. 添加完整的类型注解和文档字符串
3. 编写相应的单元测试
4. 更新相关文档

### 配置模板贡献

1. 提供高质量的默认配置
2. 包含详细的使用说明
3. 考虑不同使用场景（科学出版、演示文稿等）
4. 确保配置的可访问性（色盲友好等）
