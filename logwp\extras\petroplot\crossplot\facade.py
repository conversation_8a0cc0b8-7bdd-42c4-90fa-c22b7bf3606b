"""logwp.extras.petroplot.crossplot.facade - 交会图组件门面

本模块提供了交会图组件的公共API，包括用于工作流的步骤门面
和用于独立调用的普通门面。

Architecture
------------
层次/依赖: petroplot/crossplot的公共API层，被外部工作流或脚本调用
设计原则: 遵循《可追踪机器学习组件开发框架》的门面模式，负责编排绘图流程
"""

from __future__ import annotations

import numpy as np
from typing import Dict, Optional, Any, List
import json
import copy
from pathlib import Path
import pandas as pd
import plotly.graph_objects as go

from logwp.infra import get_logger
from logwp.extras.tracking import RunContext
from logwp.models.datasets.bundle import WpDataFrameBundle
from logwp.extras.plotting import PlotStyle, registry
from logwp.models.curve import CurveExpansionMode
from logwp.extras.petroplot.common import save_and_register_plotly_plots

from .config import CrossPlotConfig, CrossPlotColumnSelectors, SeriesColumnSelectors
from .internal.plotter import CrossPlotter
from .constants import CrossPlotProfiles, CrossPlotArtifacts
from .plotting import replot_crossplot
from .artifact_handler import CrossPlotArtifactHandler

logger = get_logger(__name__)


def _resolve_selectors(
    config: CrossPlotConfig,
    bundles: Dict[str, WpDataFrameBundle]
) -> CrossPlotColumnSelectors:
    """将用户配置中的逻辑曲线名解析为物理列名 ("翻译官"模式)。"""
    resolved_series_selectors = []
    for series_cfg in config.series:
        bundle_name = series_cfg.bundle_name
        if bundle_name not in bundles:
            logger.warning(f"系列 '{series_cfg.id}' 的数据源 '{bundle_name}' 未在传入的bundles中找到，将跳过。")
            continue

        bundle = bundles[bundle_name]
        meta = bundle.curve_metadata

        def _resolve(curve_name: Optional[str]) -> Optional[str]:
            """解析单个曲线名，增加健壮性检查。"""
            if not curve_name:
                return None
            resolved_names = meta.expand_curve_names([curve_name], mode=CurveExpansionMode.DATAFRAME)
            if not resolved_names:
                logger.warning(f"逻辑曲线名 '{curve_name}' 未能解析到任何物理列名，将被忽略。")
                return None
            if len(resolved_names) > 1:
                logger.warning(
                    f"逻辑曲线名 '{curve_name}' 解析到多个物理列: {resolved_names}。 "
                    f"将只使用第一个: '{resolved_names[0]}'"
                )
            return resolved_names[0]

        def _resolve_list(curve_names: Optional[List[str]]) -> Optional[List[str]]:
            return [c for c in (_resolve(cn) for cn in curve_names) if c] if curve_names else None

        z_col = _resolve(series_cfg.z_curve)

        # --- New Logic for Log Transform ---
        # 确定要使用的colorbar配置（系列特定或全局）
        colorbar_config = series_cfg.colorbar or config.colorbar
        if z_col and colorbar_config and colorbar_config.log_transform:
            # 直接修改传入的bundle中的DataFrame
            df = bundles[bundle_name].data
            if z_col in df.columns:
                log_z_col = f"{z_col}_log10"
                original_series = df[z_col]

                if pd.api.types.is_numeric_dtype(original_series):
                    # 在对数变换前截断非正值
                    clipped_series = original_series.clip(lower=1e-9)
                    num_clipped = (original_series <= 1e-9).sum()
                    if num_clipped > 0:
                        logger.warning(
                            f"在对数变换前，曲线 '{z_col}' 中有 {num_clipped} 个非正值被截断为 1e-9。",
                            series_id=series_cfg.id
                        )

                    # 添加新的对数变换列
                    df[log_z_col] = np.log10(clipped_series)

                    # 更新z_col以指向新列
                    z_col = log_z_col
                else:
                    logger.warning(
                        f"无法对非数值型曲线 '{z_col}' 进行对数变换。",
                        series_id=series_cfg.id
                    )
        # --- End of New Logic ---

        series_selector = SeriesColumnSelectors(
            id=series_cfg.id,
            bundle_name=series_cfg.bundle_name,
            type=series_cfg.type,
            x_col=_resolve(series_cfg.x_curve),
            y_col=_resolve(series_cfg.y_curve),
            z_col=z_col,
            error_x_col=_resolve(series_cfg.error_bars.x_values),
            error_y_col=_resolve(series_cfg.error_bars.y_values),
            hover_extra_cols=_resolve_list(series_cfg.hover_extra_curves),
            split_col=_resolve(series_cfg.split_by_curve),
            marker=series_cfg.marker,
            line=series_cfg.line,
            error_bars=series_cfg.error_bars,
            colorbar=series_cfg.colorbar,
            colorscale=series_cfg.colorscale,
            showlegend=series_cfg.showlegend,
            legendgroup=series_cfg.legendgroup,
        )
        resolved_series_selectors.append(series_selector)

    return CrossPlotColumnSelectors(series=resolved_series_selectors)


def run_crossplot_step(
    config: CrossPlotConfig,
    ctx: RunContext,
    bundles: Dict[str, WpDataFrameBundle],
    *,
    prefix: str,
) -> Dict[str, Any]:
    """执行交会图绘制步骤，支持多系列、多数据源和绘图拆分。"""
    logger.info("===== Crossplot Step Started =====")
    step_dir = ctx.get_step_dir(f"{prefix}_crossplot")

    # 1. 解析选择器 & 准备数据
    column_selectors = _resolve_selectors(config, bundles)
    if not column_selectors.series:
        logger.warning("没有可绘制的系列，步骤提前结束。")
        return {"status": "skipped", "message": "No valid series to plot."}
    data_dict = {name: b.data for name, b in bundles.items()}

    # 2. 检查是否需要拆分绘图
    split_col_selector = next((s for s in column_selectors.series if s.split_col), None)

    # 3. 生成图表
    figs_dict: Dict[str, go.Figure] = {}
    if not split_col_selector:
        logger.info("正在生成单个交会图...")
        plotter = CrossPlotter(config)
        figs_dict["main"] = plotter.create_plot(data_dict, column_selectors)
    else:
        split_df = data_dict.get(split_col_selector.bundle_name)
        if split_df is None or split_col_selector.split_col not in split_df.columns:
            logger.error(f"用于拆分的列 '{split_col_selector.split_col}' 在数据源 '{split_col_selector.bundle_name}' 中未找到。")
            return {"status": "failed", "message": "Split-by column not found."}

        unique_values = sorted(split_df[split_col_selector.split_col].dropna().unique())
        logger.info(f"找到 {len(unique_values)} 个唯一拆分值: {list(unique_values)}")

        for value in unique_values:
            logger.info(f"正在为拆分值 '{value}' 生成图表...")
            # 修正重大逻辑错误：
            # 原逻辑: `... if split_col in df.columns else df` 会错误地包含不相关数据源的全部数据。
            # 新逻辑: 只包含那些拥有拆分列并能成功过滤出数据的DataFrame，从根本上保证了拆分绘图的正确性。
            split_data_dict = {
                name: df[df[split_col_selector.split_col] == value]
                for name, df in data_dict.items()
                if split_col_selector.split_col in df.columns
            }
            # 如果过滤后所有数据帧都为空，则跳过
            if all(df.empty for df in split_data_dict.values()):
                logger.warning(f"拆分值 '{value}' 的数据为空，已跳过。")
                continue

            split_config = config.model_copy(deep=True)
            split_config.figure.title = f"{config.figure.title} ({value})"
            plotter = CrossPlotter(split_config)
            figs_dict[str(value)] = plotter.create_plot(split_data_dict, column_selectors)

    # 4. 保存并注册产物
    export_cfg = config.export
    plot_paths = save_and_register_plotly_plots(
        ctx=ctx,
        figs_dict=figs_dict,
        step_dir=step_dir,
        prefix=prefix,
        plot_base_name_template="crossplot_plot_{split_value}",
        artifact_plot_prefix=CrossPlotArtifacts.PLOT_PREFIX.value,
        output_formats=export_cfg.formats,
        width_px=export_cfg.width_pixels,
        height_px=export_cfg.height_pixels,
    )

    # 5. 保存逻辑配置和数据快照以实现可复现性
    logger.info("保存逻辑配置和数据快照以实现可复现性...")
    handler = CrossPlotArtifactHandler()

    # 5a. 保存逻辑配置
    logic_config_dict = {
        "config": config.model_dump(mode='json'),
        "selectors": column_selectors.model_dump(mode='json')
    }
    logic_config_path = step_dir / f"{CrossPlotArtifacts.LOGIC_CONFIG.value.split('.')[-1]}.json"
    handler.save_logic_config(logic_config_dict, logic_config_path)
    ctx.register_artifact(
        artifact_path=logic_config_path.relative_to(ctx.run_dir),
        artifact_name=CrossPlotArtifacts.LOGIC_CONFIG.value,
        description="用于生成图表的完整逻辑配置（包括已解析的列名）。"
    )

    # 5b. 保存数据快照
    snapshot_dir = step_dir / CrossPlotArtifacts.DATA_SNAPSHOT_DIR.value.split('.')[-1]
    for bundle_name, df in data_dict.items():
        df_path = snapshot_dir / f"{bundle_name}.csv"
        handler.save_dataframe(df, df_path)

    ctx.register_artifact(
        artifact_path=snapshot_dir.relative_to(ctx.run_dir),
        artifact_name=CrossPlotArtifacts.DATA_SNAPSHOT_DIR.value,
        description="用于生成图表的所有数据源的数据快照目录。"
    )

    logger.info("===== Crossplot Step Finished =====")
    return {"status": "completed", "plots": plot_paths}


def replot_crossplot_from_snapshot(
    snapshot_dir: Path,
    logic_config_path: Path,
    plot_style: PlotStyle,
    output_path: Path,
) -> None:
    """从快照文件精确复现交会图。

    这是一个便捷的门面函数，它调用内部的绘图逻辑来完成复现。
    """
    replot_crossplot(
        snapshot_dir=snapshot_dir,
        logic_config_path=logic_config_path,
        plot_style=plot_style,
        output_path=output_path,
    )
