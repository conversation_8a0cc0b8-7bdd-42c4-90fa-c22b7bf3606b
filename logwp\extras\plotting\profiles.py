"""logwp.extras.plotting.profiles - 核心数据模型

定义绘图配置系统的核心数据结构，包括SaveConfig和PlotProfile。

Architecture
------------
层次/依赖: logwp.extras.plotting包核心层
设计原则: 数据驱动配置、不可变对象、类型安全
性能特征: JSON序列化、深度合并、配置缓存

遵循CCG规范：
- TS-1: 完整类型注解覆盖
- DV-1: pydantic v2模型使用
- DV-4: 不可变模型设计
- CT-1: 分层枚举常量

References
----------
- 《绘图系统全新重构设计文档》§1.2 - 核心数据模型设计
- 《SCAPE_CCG_编码与通用规范》§DV - 数据验证规范
"""

from __future__ import annotations

import json
import dataclasses
from copy import deepcopy
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict, Optional, Union, Type, TypeVar, TYPE_CHECKING

if TYPE_CHECKING:
    from .registry import PlottingConfigRegistry

from .exceptions import ProfileIOError, ProfileMergeError, ProfileNotFoundError


# 延迟导入logger避免循环依赖
def _get_logger():
    try:
        from logwp.infra import get_logger
        return get_logger(__name__)
    except ImportError:
        import logging
        return logging.getLogger(__name__)

# 用于类型提示，确保with_updates在子类上也能返回正确的类型
T_PlotProfile = TypeVar("T_PlotProfile", bound="PlotProfile")


@dataclass(frozen=True)
class SaveConfig:
    """图像保存配置。

    专门用于配置图表的保存参数，与绘图样式完全分离。
    支持多种格式同时保存和高质量输出。

    Attributes:
        format (str | list[str]): 保存格式，支持单个或多个格式
            如 "png" 或 ["png", "svg", "pdf"]
        dpi (int): 图像分辨率，默认300适合出版质量
        width (float | None): 图像宽度（英寸），None表示使用figure默认值
        height (float | None): 图像高度（英寸），None表示使用figure默认值
        bbox_inches (str): 边界框设置，"tight"自动裁剪空白
        transparent (bool): 是否使用透明背景
        save_kwargs (Dict[str, Any]): 传递给matplotlib savefig的额外参数

    Examples:
        >>> # 高质量PNG保存
        >>> config = SaveConfig(format="png", dpi=300, transparent=True)
        >>>
        >>> # 多格式保存
        >>> config = SaveConfig(format=["png", "svg"], dpi=300)
        >>>
        >>> # 自定义尺寸
        >>> config = SaveConfig(format="pdf", width=8.0, height=6.0)
    """
    format: Union[str, list[str]] = "png"
    dpi: int = 300
    width: Optional[float] = None
    height: Optional[float] = None
    bbox_inches: str = "tight"
    transparent: bool = False
    save_kwargs: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self) -> None:
        """验证配置参数有效性。"""
        if self.dpi <= 0:
            raise ValueError(f"DPI必须大于0，当前值: {self.dpi}")

        if self.width is not None and self.width <= 0:
            raise ValueError(f"宽度必须大于0，当前值: {self.width}")

        if self.height is not None and self.height <= 0:
            raise ValueError(f"高度必须大于0，当前值: {self.height}")

    def get_preferred_extension(self) -> str:
        """获取首选文件扩展名。

        Returns:
            str: 文件扩展名，包含前导点（如 '.png'）
        """
        if isinstance(self.format, list):
            # 如果是列表，返回第一个格式
            preferred_format = self.format[0] if self.format else "png"
        else:
            # 如果是字符串，直接使用
            preferred_format = self.format

        # 确保扩展名有前导点
        if not preferred_format.startswith('.'):
            preferred_format = f'.{preferred_format}'

        return preferred_format


@dataclass
class PlotProfile:
    """绘图配置文件（模板）。

    这是整个绘图配置系统的核心数据结构，封装了一个图表类型的
    所有样式和保存配置。支持继承、合并和JSON序列化。

    Attributes:
        name (str): 配置模板的唯一名称，如 "plt_analyzer.capture_curve"
        save_config (Optional[SaveConfig]): 图像保存配置
        rc_params (Dict[str, Any]): matplotlib.rcParams全局样式参数
        figure_props (Dict[str, Any]): 传递给plt.figure()的参数
        title_props (Dict[str, Any]): 传递给ax.set_title()的参数
        label_props (Dict[str, Any]): 传递给ax.set_xlabel/ylabel()的参数
        artist_props (Dict[str, Any]): 传递给绘图函数的参数

    Design Notes:
        - rc_params: 设定全局样式和默认值，如字体、网格、颜色等
        - figure_props: 控制整个画布属性，如尺寸、DPI、布局等
        - title_props: 精细控制图表标题的所有属性
        - label_props: 控制坐标轴标签的样式和位置
        - artist_props: 直接控制数据绘制的样式，是最灵活的配置

    Examples:
        >>> # 创建基础配置
        >>> profile = PlotProfile(
        ...     name="test.profile",
        ...     rc_params={"font.size": 12, "axes.grid": True},
        ...     figure_props={"figsize": (8, 6)},
        ...     title_props={"fontsize": 16, "fontweight": "bold"},
        ...     artist_props={"color": "blue", "linewidth": 2}
        ... )
        >>>
        >>> # 保存到JSON
        >>> profile.to_json("config/test_profile.json")
        >>>
        >>> # 从JSON加载
        >>> loaded = PlotProfile.from_json("config/test_profile.json")
    """
    name: str
    save_config: Optional[SaveConfig] = None
    rc_params: Dict[str, Any] = field(default_factory=dict)
    figure_props: Dict[str, Any] = field(default_factory=dict)
    title_props: Dict[str, Any] = field(default_factory=dict)
    label_props: Dict[str, Any] = field(default_factory=dict)
    artist_props: Dict[str, Any] = field(default_factory=dict)
    is_frozen: bool = field(default=True, repr=False)

    def to_json(self, filepath: Union[str, Path]) -> None:
        """将配置保存到JSON文件。

        Args:
            filepath: 保存路径

        Raises:
            ProfileIOError: 文件保存失败
        """
        try:
            filepath = Path(filepath)
            filepath.parent.mkdir(parents=True, exist_ok=True)

            # 准备序列化数据
            data = {
                "config_type": "PlotProfile",
                "name": self.name,
                "save_config": self._serialize_save_config(),
                "rc_params": self.rc_params,
                "figure_props": self.figure_props,
                "title_props": self.title_props,
                "label_props": self.label_props,
                "artist_props": self.artist_props,
                "is_frozen": self.is_frozen,
            }

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            _get_logger().info("配置模板已保存", profile_name=self.name, path=str(filepath))

        except Exception as e:
            raise ProfileIOError(
                f"保存配置模板失败: {e}",
                file_path=str(filepath),
                operation="save"
            ) from e

    @classmethod
    def from_json(cls, filepath: Union[str, Path]) -> PlotProfile:
        """从JSON文件加载配置。

        Args:
            filepath: 配置文件路径

        Returns:
            PlotProfile: 加载的配置模板

        Raises:
            ProfileIOError: 文件加载失败
        """
        try:
            filepath = Path(filepath)

            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 反序列化SaveConfig
            save_config = None
            if data.get("save_config"):
                save_config = SaveConfig(**data["save_config"])

            profile = cls(
                name=data["name"],
                save_config=save_config,
                rc_params=data.get("rc_params", {}),
                figure_props=data.get("figure_props", {}),
                title_props=data.get("title_props", {}),
                label_props=data.get("label_props", {}),
                artist_props=data.get("artist_props", {}),
                is_frozen=data.get("is_frozen", True),
            )

            _get_logger().info("配置模板已加载", profile_name=profile.name, path=str(filepath))
            return profile

        except Exception as e:
            raise ProfileIOError(
                f"加载配置模板失败: {e}",
                file_path=str(filepath),
                operation="load"
            ) from e

    def merge_with_base(self, base_profile: PlotProfile) -> PlotProfile:
        """与基础模板合并，创建新的配置实例。

        实现深度合并逻辑，当前配置会覆盖基础配置的同名属性。
        对于字典类型的属性，执行递归合并而非简单替换。

        Args:
            base_profile: 基础配置模板

        Returns:
            PlotProfile: 合并后的新配置实例

        Raises:
            ProfileMergeError: 合并过程中发生错误
        """
        try:
            # 深度合并字典属性
            merged_rc_params = self._deep_merge_dict(
                base_profile.rc_params, self.rc_params
            )
            merged_figure_props = self._deep_merge_dict(
                base_profile.figure_props, self.figure_props
            )
            merged_title_props = self._deep_merge_dict(
                base_profile.title_props, self.title_props
            )
            merged_label_props = self._deep_merge_dict(
                base_profile.label_props, self.label_props
            )
            merged_artist_props = self._deep_merge_dict(
                base_profile.artist_props, self.artist_props
            )

            # 保存配置优先使用当前配置，回退到基础配置
            merged_save_config = self.save_config or base_profile.save_config

            return PlotProfile(
                name=self.name,  # 保持当前名称
                save_config=merged_save_config,
                rc_params=merged_rc_params,
                figure_props=merged_figure_props,
                title_props=merged_title_props,
                label_props=merged_label_props,
                artist_props=merged_artist_props,
            )

        except Exception as e:
            raise ProfileMergeError(
                f"配置模板合并失败: {e}",
                base_profile_name=base_profile.name,
                target_profile_name=self.name,
                merge_stage="deep_merge"
            ) from e

    def resolve(self: T_PlotProfile, registry: "PlottingConfigRegistry") -> T_PlotProfile:
        """执行三级层叠合并。

        此方法将合并逻辑内聚到PlotProfile自身，使注册表可以多态地处理配置。

        Args:
            registry: 用于查找基础模板的配置注册表实例。

        Returns:
            一个经过完整层叠合并的新的PlotProfile实例。
        """
        # mypy doesn't handle self-typing well with chained calls, so we help it.
        result: T_PlotProfile = self

        # 第一层：与模块级基础模板合并
        module_base_name = self._extract_module_base_name(self.name)
        if module_base_name:
            try:
                module_base = registry.get_base(module_base_name, expected_type=PlotProfile, clone=False)
                result = result.merge_with_base(module_base)  # type: ignore
                _get_logger().debug("已合并模块级基础模板", target=self.name, module_base=module_base_name)
            except ProfileNotFoundError:
                pass  # 模块级基础模板是可选的

        # 第二层：与全局根模板合并
        try:
            global_base = registry.get_base("base", expected_type=PlotProfile, clone=False)
            result = result.merge_with_base(global_base)  # type: ignore
            _get_logger().debug("已合并全局根模板", target=self.name, global_base="base")
        except ProfileNotFoundError:
            _get_logger().warning("全局基础模板 'base' 未找到，跳过合并。")

        return result

    def _extract_module_base_name(self, profile_name: str) -> Optional[str]:
        """从配置名称提取模块级基础模板名称。"""
        if "." not in profile_name:
            return None

        module_prefix = profile_name.split(".")[0]
        return f"{module_prefix}.base"

    def with_updates(self: T_PlotProfile, **kwargs: Any) -> T_PlotProfile:
        """创建一个包含指定更新的新的PlotProfile副本。

        此方法实现了优雅的“建造者”模式，允许用户以声明式的方式
        修改配置，而无需处理内部复杂的不可变对象复制逻辑。
        返回的副本是可变的（is_frozen=False），允许进一步的精细调整。

        Args:
            **kwargs: 要更新的属性，键为PlotProfile的字段名，
                      值为新的配置字典或值。

        Returns:
            一个全新的、可修改的PlotProfile实例。

        Examples:
            >>> original = registry.get("some.profile")
            >>> modified = original.with_updates(
            ...     title_props={"label": "New Title"},
            ...     save_config={"format": "svg", "dpi": 600}
            ... )
            >>> # 返回的modified对象是可变的
            >>> modified.figure_props["figsize"] = (10, 8)
        """
        # 1. 获取当前实例的所有字段值作为基础，同时保持对象类型（不递归转换为字典）
        # 这是修复'dict' object has no attribute 'get_preferred_extension'错误的关键
        current_attrs = {f.name: getattr(self, f.name) for f in dataclasses.fields(self)}

        current_attrs.pop('name', None)  # name将通过显式参数传递，避免重复
        current_attrs.pop('is_frozen', None)  # is_frozen 将被特殊处理

        # 2. 遍历用户提供的更新
        for key, value in kwargs.items():
            if key not in current_attrs:
                _get_logger().warning(f"未知的PlotProfile属性: '{key}'，将被忽略。")
                continue

            if key == "save_config" and isinstance(value, dict):
                # 特殊处理SaveConfig：使用dataclasses.replace创建新实例
                base_save_config = self.save_config or SaveConfig()
                current_attrs[key] = dataclasses.replace(base_save_config, **value)
            elif isinstance(current_attrs.get(key), dict) and isinstance(value, dict):
                # 对于字典属性，执行深度合并
                current_attrs[key] = self._deep_merge_dict(current_attrs[key], value)
            else:
                # 对于其他类型，直接替换
                current_attrs[key] = value

        # 3. 创建最终的PlotProfile新实例
        current_attrs['is_frozen'] = False  # 返回的实例是可变的
        return type(self)(name=self.name, **current_attrs)

    def _serialize_save_config(self) -> Optional[Dict[str, Any]]:
        """序列化SaveConfig为字典。"""
        if self.save_config is None:
            return None

        if isinstance(self.save_config, SaveConfig):
            return dataclasses.asdict(self.save_config)
        return self.save_config  # Assume it's already a dict

    def _deep_merge_dict(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并两个字典。

        Args:
            base: 基础字典
            override: 覆盖字典

        Returns:
            Dict[str, Any]: 合并后的字典
        """
        result = deepcopy(base)

        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                # 递归合并嵌套字典
                result[key] = self._deep_merge_dict(result[key], value)
            else:
                # 直接覆盖
                result[key] = deepcopy(value)

        return result


# --- 默认全局基础模板 ---
from . import theme_constants as themes

_default_global_base_profile = PlotProfile(
    name="base",
    save_config=SaveConfig(
        format=["png"],
        dpi=150,
        transparent=False,
        bbox_inches="tight"
    ),
    rc_params={
        "font.family": themes.DEFAULT_FONT_FAMILY,
        "font.size": themes.FONT_SIZE_TICKS,
        "figure.facecolor": themes.BACKGROUND_CANVAS_LIGHT,
        "axes.grid": False,
        "grid.alpha": 0.3,
    },
    figure_props={
        "dpi": 150,
        "layout": "constrained",
    }
)
