# 绘图配置清单 (Plotting Configuration Manifest)

本文档由 `scripts/plotting/export_profiles.py` 自动生成，请勿手动编辑。

## 📊 配置统计

- **总配置数**: 19
  - `PlotProfile` (Matplotlib): 18
  - `PlotStyle` (Plotly): 1
- **基础模板数**: 6
  - `PlotProfile` (Matplotlib): 5
  - `PlotStyle` (Plotly): 1

---

## 📜 PlotProfile (Matplotlib)

### 基础模板
- `base.PlotProfile.json`
- `log_scout.base.PlotProfile.json`
- `obmiq.base.PlotProfile.json`
- `swift_pso.base.PlotProfile.json`
- `validation.plt.base.PlotProfile.json`

### 具体模板
- `log_scout.boxplot.PlotProfile.json`
- `log_scout.clustermap.PlotProfile.json`
- `log_scout.heatmap.PlotProfile.json`
- `log_scout.pairplot.PlotProfile.json`
- `log_scout.regplot.PlotProfile.json`
- `obmiq.captum_ig_summary.PlotProfile.json`
- `obmiq.crossplot.PlotProfile.json`
- `obmiq.grad_cam.PlotProfile.json`
- `obmiq.residuals_hist.PlotProfile.json`
- `obmiq.residuals_plot.PlotProfile.json`
- `obmiq.shap_summary.PlotProfile.json`
- `obmiq.training_history.PlotProfile.json`
- `swift_pso.tsne_cluster_analysis.PlotProfile.json`
- `swift_pso.tsne_convergence.PlotProfile.json`
- `validation.perm_corr.permeability_crossplot.PlotProfile.json`
- `validation.plt.capture_curve.PlotProfile.json`
- `validation.plt.contribution_crossplot.PlotProfile.json`
- `validation.plt.lorenz_curve.PlotProfile.json`

---

## 🎨 PlotStyle (Plotly)

### 基础模板
- `base.PlotStyle.json`

### 具体模板
- `petroplot.crossplot.default.PlotStyle.json`

---

## 🚀 使用方法

### 在代码中获取配置

```python
from logwp.extras.plotting import registry, PlotProfile, PlotStyle

# 获取一个 Matplotlib profile (自动继承合并)
profile = registry.get("validation.contribution_crossplot", expected_type=PlotProfile)

# 获取一个 Plotly style (自动继承合并)
style = registry.get("petroplot.crossplot.default", expected_type=PlotStyle)
```

### 从文件自定义配置

```bash
# 1. 复制一个现有的配置文件
cp config/plot_profiles/validation.contribution_crossplot.PlotProfile.json my_custom_profile.json

# 2. 编辑 my_custom_profile.json 文件...
```

```python
# 3. 在代码中加载并注册你的自定义配置
from logwp.extras.plotting import PlotProfile, registry

my_profile = PlotProfile.from_json("my_custom_profile.json")
registry.register(my_profile)

# 现在你可以像使用内置配置一样获取它
profile = registry.get("my_custom_profile", expected_type=PlotProfile)
```