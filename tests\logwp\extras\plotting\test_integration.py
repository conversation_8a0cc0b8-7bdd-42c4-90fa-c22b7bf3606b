"""集成测试 - 绘图配置服务完整工作流。

测试整个绘图系统的端到端功能，包括：
- 完整的配置创建、注册、获取、应用工作流
- 两级继承体系的实际使用
- JSON配置文件的完整生命周期
- 与matplotlib的完整集成
- 错误场景的端到端处理
"""

import json
import pytest
from pathlib import Path

from logwp.extras.plotting import (
    registry as global_registry,
    PlotProfile,
    SaveConfig,
    apply_profile,
    save_figure
)
from logwp.extras.plotting.exceptions import ProfileNotFoundError


class TestEndToEndWorkflow:
    """测试端到端工作流。"""

    def test_complete_plotting_workflow(self, clean_registry, matplotlib_figure, temp_dir):
        """测试完整的绘图工作流。"""
        registry = clean_registry
        fig, ax = matplotlib_figure

        # 1. 创建并注册全局基础模板
        global_base = PlotProfile(
            name="base",
            rc_params={
                "font.family": "Arial",
                "font.size": 10,
                "figure.facecolor": "white"
            },
            figure_props={
                "dpi": 150,
                "layout": "constrained"
            }
        )
        registry.register_base(global_base)

        # 2. 创建并注册模块级基础模板
        module_base = PlotProfile(
            name="analysis.base",
            rc_params={
                "axes.grid": True,
                "grid.alpha": 0.3
            },
            figure_props={
                "figsize": (10, 8)
            }
        )
        registry.register_base(module_base)

        # 3. 创建并注册具体图表配置
        specific_chart = PlotProfile(
            name="analysis.scatter_plot",
            title_props={
                "label": "Data Analysis Scatter Plot",
                "fontsize": 16,
                "fontweight": "bold"
            },
            label_props={
                "xlabel": "X Variable",
                "ylabel": "Y Variable",
                "fontsize": 12
            },
            artist_props={
                "scatter": {
                    "s": 50,
                    "alpha": 0.7,
                    "c": "blue",
                    "edgecolor": "black"
                },
                "line": {
                    "color": "red",
                    "linewidth": 2,
                    "linestyle": "--"
                }
            },
            save_config=SaveConfig(
                format=["png", "svg"],
                dpi=300,
                transparent=False
            )
        )
        registry.register(specific_chart)

        # 4. 获取合并后的配置
        merged_profile = registry.get("analysis.scatter_plot", expected_type=PlotProfile)

        # 验证继承链正确合并
        assert merged_profile.rc_params["font.family"] == "Arial"      # 全局base
        assert merged_profile.rc_params["font.size"] == 10             # 全局base
        assert merged_profile.rc_params["axes.grid"] is True           # 模块base
        assert merged_profile.figure_props["dpi"] == 150               # 全局base
        assert merged_profile.figure_props["figsize"] == (10, 8)       # 模块base
        assert merged_profile.title_props["label"] == "Data Analysis Scatter Plot"  # 具体配置

        # 5. 应用配置到matplotlib
        apply_profile(ax, merged_profile)

        # 验证样式已应用
        assert ax.get_title() == "Data Analysis Scatter Plot"
        assert ax.get_xlabel() == "X Variable"
        assert ax.get_ylabel() == "Y Variable"
        assert fig.get_size_inches().tolist() == [10.0, 8.0]

        # 6. 绘制数据
        x_data = [1, 2, 3, 4, 5]
        y_data = [2, 4, 1, 5, 3]

        # 使用配置中的样式参数
        scatter_props = merged_profile.artist_props.get("scatter", {})
        line_props = merged_profile.artist_props.get("line", {})

        ax.scatter(x_data, y_data, **scatter_props)
        ax.plot(x_data, y_data, **line_props)

        # 7. 保存图像
        save_config = merged_profile.save_config
        saved_paths = save_figure(fig, save_config, temp_dir, "integration_test")

        # 验证保存结果
        assert len(saved_paths) == 2
        png_path = temp_dir / "integration_test.png"
        svg_path = temp_dir / "integration_test.svg"

        assert png_path in saved_paths
        assert svg_path in saved_paths
        assert png_path.exists()
        assert svg_path.exists()

    def test_json_configuration_lifecycle(self, clean_registry, temp_dir):
        """测试JSON配置文件的完整生命周期。"""
        registry = clean_registry

        # 1. 创建配置并保存到JSON
        original_profile = PlotProfile(
            name="json_test.chart",
            rc_params={"font.size": 14, "axes.grid": True},
            figure_props={"figsize": (12, 8), "dpi": 200},
            title_props={"label": "JSON Test Chart", "fontsize": 18},
            save_config=SaveConfig(format="pdf", dpi=300)
        )

        json_file = temp_dir / "test_chart.json"
        original_profile.to_json(json_file)

        # 验证JSON文件存在且内容正确
        assert json_file.exists()
        with open(json_file, 'r', encoding='utf-8') as f:
            json_data = json.load(f)

        assert json_data["name"] == "json_test.chart"
        assert json_data["rc_params"]["font.size"] == 14

        # 2. 从JSON加载配置
        loaded_profile = PlotProfile.from_json(json_file)

        # 验证加载的配置与原始配置相同
        assert loaded_profile.name == original_profile.name
        assert loaded_profile.rc_params == original_profile.rc_params
        # 注意：JSON序列化会将tuple转换为list，这是正常的
        assert loaded_profile.figure_props["dpi"] == original_profile.figure_props["dpi"]
        assert loaded_profile.figure_props["figsize"] == list(original_profile.figure_props["figsize"])
        assert loaded_profile.title_props == original_profile.title_props
        assert loaded_profile.save_config.format == original_profile.save_config.format

        # 3. 注册加载的配置
        registry.register(loaded_profile)

        # 4. 从注册表获取配置
        retrieved_profile = registry.get("json_test.chart", expected_type=PlotProfile)

        # 验证获取的配置正确
        assert retrieved_profile.name == "json_test.chart"
        assert retrieved_profile.rc_params["font.size"] == 14

        # 5. 批量保存所有配置到目录
        config_dir = temp_dir / "config_export"
        registry.save_to_dir(config_dir)

        # 验证配置文件已导出
        exported_file = config_dir / "json_test.chart.PlotProfile.json"
        assert exported_file.exists()

        # 6. 从目录批量加载配置
        new_registry = clean_registry.__class__()  # 创建新的注册表实例
        new_registry.load_from_dir(config_dir)

        # 验证配置已加载到新注册表
        profiles = new_registry.list_profiles(expected_type=PlotProfile)
        assert "json_test.chart" in profiles

        final_profile = new_registry.get("json_test.chart", expected_type=PlotProfile)
        assert final_profile.rc_params["font.size"] == 14

    def test_complex_inheritance_scenario(self, clean_registry):
        """测试复杂继承场景。"""
        registry = clean_registry

        # 创建多层次的配置体系
        # 1. 全局基础模板 - 公司标准
        company_base = PlotProfile(
            name="base",
            rc_params={
                "font.family": "Arial",
                "font.size": 10,
                "figure.facecolor": "white",
                "axes.edgecolor": "black"
            },
            figure_props={
                "dpi": 150
            }
        )

        # 2. 项目级基础模板 - SCAPE项目标准
        scape_base = PlotProfile(
            name="scape.base",
            rc_params={
                "font.size": 12,        # 覆盖公司标准
                "axes.grid": True,      # 项目特定
                "grid.alpha": 0.3
            },
            figure_props={
                "layout": "constrained"  # 项目特定
            }
        )

        # 3. 模块级基础模板 - 验证模块标准（修正为一级模块）
        validation_base = PlotProfile(
            name="validation.base",
            rc_params={
                "axes.grid": True,
                "grid.linestyle": "--"   # 模块特定
            },
            figure_props={
                "figsize": (8, 8)       # 验证图表标准尺寸
            }
        )

        # 4. 具体图表配置 - PLT分析图
        plt_chart = PlotProfile(
            name="validation.plt_analysis",
            title_props={
                "label": "PLT Analysis Results",
                "fontsize": 16
            },
            artist_props={
                "scatter": {"s": 60, "alpha": 0.7},
                "line": {"linewidth": 2}
            }
        )

        # 注册所有配置
        registry.register_base(company_base)
        registry.register_base(scape_base)
        registry.register_base(validation_base)
        registry.register(plt_chart)

        # 获取最终合并的配置
        final_config = registry.get("validation.plt_analysis", expected_type=PlotProfile)

        # 验证复杂继承链的结果
        # 应该是：company_base -> scape.base -> validation.base -> plt_chart

        # 从公司基础继承
        assert final_config.rc_params["font.family"] == "Arial"
        assert final_config.rc_params["figure.facecolor"] == "white"
        assert final_config.figure_props["dpi"] == 150

        # 从项目基础覆盖/新增
        # 注意：validation.plt_analysis只会继承validation.base，不会继承scape.base
        # 因为当前的继承逻辑只支持一级模块继承
        assert final_config.rc_params["font.size"] == 10   # 来自全局base（没有被覆盖）
        assert final_config.rc_params["axes.grid"] is True  # 来自validation.base
        assert final_config.figure_props["dpi"] == 150      # 来自全局base

        # 从模块基础新增/覆盖
        assert final_config.rc_params["grid.linestyle"] == "--"  # 来自validation.base
        assert final_config.figure_props["figsize"] == (8, 8)    # 来自validation.base

        # 从具体配置新增
        assert final_config.title_props["label"] == "PLT Analysis Results"
        assert final_config.artist_props["scatter"]["s"] == 60

    def test_error_handling_workflow(self, clean_registry):
        """测试错误处理工作流。"""
        registry = clean_registry

        # 1. 测试获取不存在的配置
        with pytest.raises(ProfileNotFoundError) as exc_info:
            registry.get("nonexistent.profile", expected_type=PlotProfile)

        assert exc_info.value.profile_name == "nonexistent.profile"
        assert exc_info.value.available_profiles == []

        # 2. 注册一些配置后再次测试
        test_profile = PlotProfile(name="test.profile")
        registry.register(test_profile)

        with pytest.raises(ProfileNotFoundError) as exc_info:
            registry.get("still.nonexistent", expected_type=PlotProfile)

        assert "test.profile" in exc_info.value.available_profiles

        # 3. 测试配置名称冲突
        from logwp.extras.plotting.exceptions import ProfileRegistrationError

        duplicate_profile = PlotProfile(name="test.profile")

        with pytest.raises(ProfileRegistrationError):
            registry.register(duplicate_profile, overwrite=False)

        # 4. 测试允许覆盖
        registry.register(duplicate_profile, overwrite=True)  # 应该成功

        # 5. 测试无效的基础模板名称
        invalid_base = PlotProfile(name="invalid_base_name")

        with pytest.raises(ProfileRegistrationError):
            registry.register_base(invalid_base)

    def test_real_world_usage_pattern(self, clean_registry, matplotlib_figure, temp_dir):
        """测试真实世界的使用模式。"""
        registry = clean_registry
        fig, ax = matplotlib_figure

        # 模拟SCAPE项目的实际使用场景

        # 1. 系统初始化 - 注册默认配置
        default_base = PlotProfile(
            name="base",
            rc_params={"font.family": "Arial", "font.size": 10},
            figure_props={"dpi": 150}
        )
        registry.register_base(default_base)

        # 2. 模块开发者定义模块标准
        plt_analyzer_base = PlotProfile(
            name="plt_analyzer.base",
            rc_params={"axes.grid": True},
            figure_props={"figsize": (8, 8)}
        )
        registry.register_base(plt_analyzer_base)

        # 3. 开发者定义具体图表配置
        capture_curve_config = PlotProfile(
            name="plt_analyzer.capture_curve",
            title_props={"label": "Top-K Capture Curve", "fontsize": 16},
            label_props={"xlabel": "Cumulative Thickness Ratio (%)", "ylabel": "Capture Ratio (%)"},
            artist_props={
                "model_curve": {"color": "blue", "linewidth": 2.5, "label": "Model"},
                "ideal_curve": {"color": "green", "linestyle": "--", "label": "Ideal"},
                "random_curve": {"color": "black", "linestyle": ":", "label": "Random"}
            }
        )
        registry.register(capture_curve_config)

        # 4. 用户使用 - 获取配置并应用
        profile = registry.get("plt_analyzer.capture_curve", expected_type=PlotProfile)
        apply_profile(ax, profile)

        # 5. 绘制实际数据
        x = [0, 20, 40, 60, 80, 100]

        # 模型曲线
        y_model = [0, 35, 60, 75, 85, 100]
        model_props = profile.artist_props.get("model_curve", {})
        ax.plot(x, y_model, **model_props)

        # 理想曲线
        y_ideal = [0, 20, 40, 60, 80, 100]
        ideal_props = profile.artist_props.get("ideal_curve", {})
        ax.plot(x, y_ideal, **ideal_props)

        # 随机曲线
        y_random = x  # 对角线
        random_props = profile.artist_props.get("random_curve", {})
        ax.plot(x, y_random, **random_props)

        ax.legend()
        ax.set_xlim(0, 100)
        ax.set_ylim(0, 100)

        # 6. 保存结果
        save_config = SaveConfig(format=["png", "svg"], dpi=300)
        saved_paths = save_figure(fig, save_config, temp_dir, "capture_curve_analysis")

        # 验证完整工作流成功
        assert len(saved_paths) == 2
        assert all(path.exists() for path in saved_paths)
        assert ax.get_title() == "Top-K Capture Curve"
        assert ax.get_xlabel() == "Cumulative Thickness Ratio (%)"

        # 7. 用户自定义 - 修改配置并保存
        custom_config = PlotProfile(
            name="plt_analyzer.capture_curve_custom",
            title_props={"label": "Custom Capture Curve", "color": "red"},
            artist_props={
                "model_curve": {"color": "purple", "linewidth": 3}
            }
        )
        registry.register(custom_config)

        # 获取自定义配置（会继承基础配置）
        custom_profile = registry.get("plt_analyzer.capture_curve_custom", expected_type=PlotProfile)

        # 验证继承和覆盖
        assert custom_profile.title_props["label"] == "Custom Capture Curve"
        assert custom_profile.artist_props["model_curve"]["color"] == "purple"
        # 注意：custom配置没有继承原始配置的artist_props，因为它们是不同的配置
        # custom配置只会继承基础模板，不会继承其他具体配置
