"""logwp.extras.petroplot.crossplot.internal.marginal_plot_helper - 边缘图辅助工具

本模块定义了 MarginalPlotHelper，一个用于处理交会图边缘分布图的辅助类。
它封装了子图创建、数据聚合和边缘图绘制的所有复杂性。
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any, Tuple
import numpy as np
import plotly.graph_objects as go
from logwp.infra import get_logger

from ..config import MarginalConfig, MarginalKind, SeriesConfig

logger = get_logger(__name__)


class MarginalDrawer(ABC):
    """Abstract base class for marginal plot drawers."""

    @abstractmethod
    def draw(self, fig: go.Figure, data: List[float], series_config: SeriesConfig,
             marginal_config: MarginalConfig, row: int, col: int,
             orientation: str = 'vertical', bins: Optional[np.ndarray] = None) -> None:
        """Draw a marginal plot on the specified subplot."""
        pass


class HistogramDrawer(MarginalDrawer):
    """Drawer for histogram marginal plots."""

    def draw(self, fig: go.Figure, data: List[float], series_config: SeriesConfig,
             marginal_config: MarginalConfig, row: int, col: int,
             orientation: str = 'vertical', bins: Optional[np.ndarray] = None) -> None:

        # 使用对应的样式配置
        style = marginal_config.hist_style

        if bins is not None:
            # Use pre-calculated logarithmic bins
            positive_data = [v for v in data if v > 0]
            if positive_data:
                counts, bin_edges = np.histogram(positive_data, bins=bins, density=True)
                bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2
                bin_widths = np.diff(bin_edges)

                if orientation == 'vertical':
                    fig.add_trace(go.Bar(
                        x=bin_centers, y=counts, width=bin_widths,
                        name=series_config.id, legendgroup=series_config.id,
                        showlegend=False,
                        marker_color=self._get_color(series_config),
                        opacity=style.opacity,
                        marker_line=dict(width=style.marker_line_width, color=style.marker_line_color)
                    ), row=row, col=col)
                else:  # horizontal
                    fig.add_trace(go.Bar(
                        y=bin_centers, x=counts, width=bin_widths, orientation='h',
                        name=series_config.id, legendgroup=series_config.id,
                        showlegend=False,
                        marker_color=self._get_color(series_config),
                        opacity=style.opacity,
                        marker_line=dict(width=style.marker_line_width, color=style.marker_line_color)
                    ), row=row, col=col)
        else:
            # Use standard histogram
            nbins = style.nbins or (marginal_config.bins if isinstance(marginal_config.bins, int) else 25)

            if orientation == 'vertical':
                fig.add_trace(go.Histogram(
                    x=data,
                    name=series_config.id,
                    legendgroup=series_config.id,
                    showlegend=False,
                    marker_color=self._get_color(series_config),
                    nbinsx=nbins,
                    histnorm=style.histnorm,
                    opacity=style.opacity,
                    marker_line=dict(width=style.marker_line_width, color=style.marker_line_color)
                ), row=row, col=col)
            else:  # horizontal
                fig.add_trace(go.Histogram(
                    y=data,
                    name=series_config.id,
                    legendgroup=series_config.id,
                    showlegend=False,
                    marker_color=self._get_color(series_config),
                    nbinsy=nbins,
                    histnorm=style.histnorm,
                    opacity=style.opacity,
                    marker_line=dict(width=style.marker_line_width, color=style.marker_line_color)
                ), row=row, col=col)

    def _get_color(self, series_config: SeriesConfig) -> str:
        """Get the color for this series."""
        return series_config.marker.facecolor or "#1f77b4"


class BoxDrawer(MarginalDrawer):
    """Drawer for box plot marginal plots."""

    def draw(self, fig: go.Figure, data: List[float], series_config: SeriesConfig,
             marginal_config: MarginalConfig, row: int, col: int,
             orientation: str = 'vertical', bins: Optional[np.ndarray] = None) -> None:

        # 使用对应的样式配置
        style = marginal_config.box_style

        if orientation == 'vertical':
            fig.add_trace(go.Box(
                x=data,
                name=series_config.id,
                legendgroup=series_config.id,
                showlegend=False,
                marker_color=self._get_color(series_config),
                # 高级配置
                boxpoints=style.boxpoints,
                boxmean=style.boxmean,
                fillcolor=style.fillcolor or self._get_color(series_config),
                opacity=style.opacity,
                line=dict(width=style.line_width),
                notched=style.notched,
                whiskerwidth=style.whiskerwidth
            ), row=row, col=col)
        else:  # horizontal
            fig.add_trace(go.Box(
                y=data,
                name=series_config.id,
                legendgroup=series_config.id,
                showlegend=False,
                marker_color=self._get_color(series_config),
                # 高级配置
                boxpoints=style.boxpoints,
                boxmean=style.boxmean,
                fillcolor=style.fillcolor or self._get_color(series_config),
                opacity=style.opacity,
                line=dict(width=style.line_width),
                notched=style.notched,
                whiskerwidth=style.whiskerwidth
            ), row=row, col=col)

    def _get_color(self, series_config: SeriesConfig) -> str:
        """Get the color for this series."""
        return series_config.marker.facecolor or "#1f77b4"

    def _get_fill_color(self, series_config: SeriesConfig) -> str:
        """Get the fill color with transparency for this series."""
        color = self._get_color(series_config)
        # Convert to rgba format with transparency
        if color.startswith('#'):
            # Convert hex to rgb with alpha
            hex_color = color.lstrip('#')
            if len(hex_color) == 6:
                r = int(hex_color[0:2], 16)
                g = int(hex_color[2:4], 16)
                b = int(hex_color[4:6], 16)
                return f'rgba({r},{g},{b},0.3)'
        return color

class ViolinDrawer(MarginalDrawer):
    """Drawer for violin plot marginal plots."""

    def draw(self, fig: go.Figure, data: List[float], series_config: SeriesConfig,
             marginal_config: MarginalConfig, row: int, col: int,
             orientation: str = 'vertical', bins: Optional[np.ndarray] = None) -> None:

        # 使用对应的样式配置
        style = marginal_config.violin_style

        if orientation == 'vertical':
            fig.add_trace(go.Violin(
                x=data,
                name=series_config.id,
                legendgroup=series_config.id,
                showlegend=False,
                line_color=self._get_color(series_config),
                fillcolor=self._get_fill_color(series_config),
                # 高级配置
                points=style.points,
                box_visible=style.box_visible,
                meanline_visible=style.meanline_visible,
                side=style.side,
                opacity=style.opacity,
                line=dict(width=style.line_width),
                scalemode=style.scalemode
            ), row=row, col=col)
        else:  # horizontal
            fig.add_trace(go.Violin(
                y=data,
                name=series_config.id,
                legendgroup=series_config.id,
                showlegend=False,
                line_color=self._get_color(series_config),
                fillcolor=self._get_fill_color(series_config),
                # 高级配置
                points=style.points,
                box_visible=style.box_visible,
                meanline_visible=style.meanline_visible,
                side=style.side,
                opacity=style.opacity,
                line=dict(width=style.line_width),
                scalemode=style.scalemode
            ), row=row, col=col)

    def _get_color(self, series_config: SeriesConfig) -> str:
        """Get the color for this series."""
        return series_config.marker.facecolor or "#1f77b4"

    def _get_fill_color(self, series_config: SeriesConfig) -> str:
        """Get the fill color with transparency for this series."""
        color = self._get_color(series_config)
        # Convert to rgba format with transparency
        if color.startswith('#'):
            # Convert hex to rgb with alpha
            hex_color = color.lstrip('#')
            if len(hex_color) == 6:
                r = int(hex_color[0:2], 16)
                g = int(hex_color[2:4], 16)
                b = int(hex_color[4:6], 16)
                return f'rgba({r},{g},{b},0.3)'
        return color


class KDEDrawer(MarginalDrawer):
    """Drawer for KDE (Kernel Density Estimation) marginal plots using Plotly's Violin."""

    def draw(self, fig: go.Figure, data: List[float], series_config: SeriesConfig,
             marginal_config: MarginalConfig, row: int, col: int,
             orientation: str = 'vertical', bins: Optional[np.ndarray] = None) -> None:

        # 使用Plotly内置的Violin图来实现KDE
        # Violin图本质上就是KDE的可视化
        if len(data) < 2:
            return

        # 使用对应的样式配置
        style = marginal_config.kde_style

        # 使用Violin图实现KDE效果
        if orientation == 'vertical':
            fig.add_trace(go.Violin(
                x=data,
                orientation='h',  # 水平方向的violin用于垂直边际图
                side=style.side,  # 使用配置的侧面显示
                width=2.0,  # 控制宽度
                name=series_config.id,
                legendgroup=series_config.id,
                showlegend=False,
                line_color=self._get_color(series_config),
                fillcolor=self._get_fill_color(series_config),
                meanline_visible=False,  # KDE通常不显示均值线
                box_visible=False,  # KDE不显示箱线图
                points=False,  # KDE不显示数据点
                scalemode='width',  # 按宽度缩放
                opacity=style.opacity,
                line=dict(width=style.line_width)
            ), row=row, col=col)
        else:  # horizontal
            fig.add_trace(go.Violin(
                y=data,
                orientation='v',  # 垂直方向的violin用于水平边际图
                side=style.side,  # 使用配置的侧面显示
                width=2.0,  # 控制宽度
                name=series_config.id,
                legendgroup=series_config.id,
                showlegend=False,
                line_color=self._get_color(series_config),
                fillcolor=self._get_fill_color(series_config),
                meanline_visible=False,  # KDE通常不显示均值线
                box_visible=False,  # KDE不显示箱线图
                points=False,  # KDE不显示数据点
                scalemode='width',  # 按宽度缩放
                opacity=style.opacity,
                line=dict(width=style.line_width)
            ), row=row, col=col)

    def _get_color(self, series_config: SeriesConfig) -> str:
        """Get the color for this series."""
        return series_config.marker.facecolor or "#1f77b4"

    def _get_fill_color(self, series_config: SeriesConfig) -> str:
        """Get the fill color with transparency for this series."""
        color = self._get_color(series_config)
        # Convert to rgba format with transparency
        if color.startswith('#'):
            # Convert hex to rgb with alpha
            hex_color = color.lstrip('#')
            if len(hex_color) == 6:
                r = int(hex_color[0:2], 16)
                g = int(hex_color[2:4], 16)
                b = int(hex_color[4:6], 16)
                return f'rgba({r},{g},{b},0.3)'
        return color

# Component Registry
MARGINAL_DRAWERS: Dict[MarginalKind, MarginalDrawer] = {
    MarginalKind.HIST: HistogramDrawer(),
    MarginalKind.BOX: BoxDrawer(),
    MarginalKind.VIOLIN: ViolinDrawer(),
    MarginalKind.KDE: KDEDrawer()
    # Additional drawers will be registered here
}


def register_marginal_drawer(kind: MarginalKind, drawer: MarginalDrawer) -> None:
    """Register a new marginal plot drawer."""
    MARGINAL_DRAWERS[kind] = drawer
