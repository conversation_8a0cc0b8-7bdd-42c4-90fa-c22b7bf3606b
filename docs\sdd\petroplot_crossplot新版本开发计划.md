# 新版本交会图组件详细开发计划

**目标**：开发一个新版本的交会图机器学习组件（`logwp\extras\petroplot\crossplot`），技术栈为 Plotly 6.0，并遵循 SCAPE 机器学习组件开发最佳实践。

**核心策略**：小步快跑，优先构建绘图核心，逐步完善外部框架。

**开发计划总览**：

1. **PlotStyle 绘图模板开发 (3天)**：
   *   在 `logwp\extras\plotting` 中定义 `PlotStyle` 数据模型。
   *   修改 `PlotProfileRegistry` 以支持 `PlotStyle` 的注册和获取。
   *   创建 `theme_constants.py` 定义全局主题常量。

2. **交会图组件框架搭建 (2天)**：
   *   复制旧版本组件框架到新版本目录。
   *   调整命名空间和文件引用。

3. **绘图核心移植与重构 (5天)**：
   *   将原型版本的绘图核心代码移植到新组件中。
   *   使用 `PlotStyle` 替换原型中的 `StyleConfig`。
   *   重构绘图逻辑，使其与新的配置系统兼容。

4. **门面函数开发 (3天)**：
   *   实现 `run_crossplot_step` 门面函数，处理数据选择和转换。
   *   实现 `replot_crossplot_from_snapshot` 函数，支持从快照复现。

5. **产物处理与注册 (2天)**：
   *   实现 `ArtifactHandler`，用于保存和加载数据快照。
   *   在门面函数中注册所有产物。

6. **可复现绘图 (2天)**：
   *   实现 `plotting.py` 中的 `replot_crossplot` 函数。
   *   创建 `plot_profiles.py`，注册默认的 `PlotProfile` 模板。

7. **测试与验证 (3天)**：
    * 编写单元测试和集成测试，确保组件的正确性和稳定性。
    * 生成各种测试图，验证绘图效果。

**总预计时间：20个工作日**

---

## 详细步骤

### 1. PlotStyle 绘图模板开发 (3天)

*   **目标**：构建与绘图后端无关的通用样式配置模型。

*   **步骤**：

    1.  **定义 `PlotStyle` 模型 (1天)**：
        *   **操作**：在 `logwp\extras\plotting\styles.py` （新建文件）中，创建一个名为 `PlotStyle` 的 `Pydantic` 模型，字段包括 `name`, `font`, `color`, `linewidth`, `marker`, `frame`, `backend_specific`。
        *   **协作模式**: 我提供 `PlotStyle` 模型的完整代码，**由您负责创建 `styles.py` 文件并粘贴代码**。
        *   **代码**：参照您提供的 `StyleConfig` 结构。
        *   **测试**：编写单元测试，确保模型可以正确创建和序列化。

    2.  **修改 `PlotProfileRegistry` (1天)**：
        *   **操作**：修改 `logwp\extras\plotting\registry.py`，允许注册和获取 `PlotStyle` 对象。增加 `get` 方法的 `expected_type` 参数，进行类型安全检查。
        *   **协作模式**: 我提供 `registry.py` 的 `diff` 更新，**由您负责应用**。
        *   **代码**：修改 `registry.register()` 和 `registry.get()` 方法。
        *   **测试**：编写单元测试，确保可以同时注册和获取 `PlotProfile` 和 `PlotStyle` 对象。

    3.  **创建 `theme_constants.py` (1天)**：
        *   **操作**：在 `logwp\extras\plotting` 目录下创建 `theme_constants.py` 文件，定义全局主题常量，如 `PRIMARY_BRAND_COLOR`, `DEFAULT_FONT_FAMILY` 等。
        *   **协作模式**: 我提供 `theme_constants.py` 的完整代码，**由您负责创建文件并粘贴代码**。
        *   **代码**：定义常量。
        *   **目的**：确保 `PlotProfile` 和 `PlotStyle` 在核心视觉元素上保持一致。

### 2. 交会图组件框架搭建 (2天)

*   **目标**：在新位置创建交会图组件的基本目录结构和文件。

*   **步骤**：

    1.  **复制目录结构 (0.5天)**：
        *   **操作**：将 `logwp\extras\petroplot\crossplot_backup` 目录完整复制到 `logwp\extras\petroplot\crossplot`。
        *   **协作模式**: **此步骤完全由您完成**。
        *   **目的**：保留旧版本组件的外部框架。

    2.  **调整命名空间 (1天)**：
        *   **操作**：修改所有文件中的命名空间引用，将 `crossplot_backup` 替换为 `crossplot`。
        *   **协作模式**: **此步骤完全由您完成**。
        *   **目的**：确保新组件使用正确的命名空间。

    3.  **更新文件引用 (0.5天)**：
        *   **操作**：检查并更新所有文件内部的模块引用，确保引用路径正确。
        *   **协作模式**: **此步骤完全由您完成**。
        *   **目的**：确保组件内部模块可以相互访问。

### 3. 绘图核心移植与重构 (5天)

*   **目标**：将原型版本的绘图核心代码移植到新组件中，并使用 `PlotStyle` 替换 `StyleConfig`。

*   **步骤**：

    1.  **移植绘图代码 (2天)**：
        *   **操作**：将 `docs\examples\crossplot_with_marginals\crossplot_plotly6_augment.py` 中的绘图核心代码（`CrossPlotter` 类和相关辅助函数）复制到 `logwp\extras\petroplot\crossplot\internal\plotter.py`。
        *   **协作模式**: **此步骤完全由您完成**。我将明确指出需要复制的代码范围。
        *   **注意**：不要复制整个文件，只复制绘图核心部分。

    2.  **替换 `StyleConfig` (1天)**：
        *   **操作**：在 `logwp\extras\petroplot\crossplot\internal\plotter.py` 中，将原型代码中使用的 `StyleConfig` 替换为新的 `PlotStyle`。
        *   **协作模式**: 我提供 `plotter.py` 的重构 `diff`，**由您负责应用**。
        *   **目的**：使用新的绘图模板系统。

    3.  **重构绘图逻辑 (2天)**：
        *   **操作**：根据新的 `PlotStyle` 调整绘图逻辑，确保可以正确读取样式参数。
        *   **操作**：移除原型代码中与 `StyleConfig` 相关的代码。
        *   **协作模式**: 我提供 `plotter.py` 的重构 `diff`，**由您负责应用**。
        *   **目的**：使绘图核心与新的配置系统兼容。

### 4. 门面函数开发 (3天)

*   **目标**：实现 `run_crossplot_step` 和 `replot_crossplot_from_snapshot` 两个门面函数。

*   **步骤**：

    1.  **实现 `run_crossplot_step` (2天)**：
        *   **操作**：在 `logwp\extras\petroplot\crossplot\facade.py` 中，实现 `run_crossplot_step` 函数，接收 `config`, `ctx`, `train_bundle` 等参数。
        *   **操作**：在函数内部，调用内部绘图核心生成图表。
        *   **操作**：实现数据选择和转换逻辑，将 `WpDataFrameBundle` 转换为绘图核心所需的数据格式。
        *   **协作模式**: 我提供 `facade.py` 的代码实现，**由您负责应用**。
        *   **注意**：遵循 SCAPE 机器学习组件开发最佳实践，实现参数解析和验证。

    2.  **实现 `replot_crossplot_from_snapshot` (1天)**：
        *   **操作**：在 `logwp\extras\petroplot\crossplot\facade.py` 中，实现 `replot_crossplot_from_snapshot` 函数，接收 `snapshot_path`, `profile`, `output_path` 等参数。
        *   **操作**：在函数内部，调用内部绘图核心生成图表。
        *   **协作模式**: 我提供 `facade.py` 中此函数的代码实现，**由您负责应用**。

### 5. 产物处理与注册 (2天)

*   **目标**：实现 `ArtifactHandler`，用于保存和加载数据快照，并在门面函数中注册所有产物。

*   **步骤**：

    1.  **实现 `ArtifactHandler` (1天)**：
        *   **操作**：在 `logwp\extras\petroplot\crossplot\artifact_handler.py` 中，创建一个名为 `CrossPlotArtifactHandler` 的类，实现 `save_dataframe` 和 `load_dataframe` 方法。
        *   **协作模式**: 我提供 `CrossPlotArtifactHandler` 的完整代码，**由您负责应用**。
        *   **目的**：用于保存和加载数据快照。

    2.  **注册产物 (1天)**：
        *   **操作**：在 `run_crossplot_step` 和 `replot_crossplot_from_snapshot` 门面函数中，使用 `ctx.register_artifact` 注册所有产物，包括图表和数据快照。
        *   **协作模式**: 我提供 `facade.py` 中注册产物部分的 `diff`，**由您负责应用**。
        *   **目的**：确保所有产物都可以被追踪和复用。

### 6. 可复现绘图 (2天)

*   **目标**：实现 `plotting.py` 中的 `replot_crossplot` 函数，并创建 `plot_profiles.py`，注册默认的 `PlotProfile` 模板。

*   **步骤**：

    1.  **实现 `replot_crossplot` (1天)**：
        *   **操作**：在 `logwp\extras\petroplot\crossplot\plotting.py` 中，实现 `replot_crossplot` 函数，接收 `snapshot_path`, `profile`, `output_path` 等参数。
        *   **操作**：在函数内部，调用内部绘图核心生成图表。
        *   **协作模式**: 我提供 `replot_crossplot` 函数的完整代码，**由您负责应用**。
        *   **目的**：支持从数据快照复现图表。

    2.  **创建 `plot_profiles.py` (1天)**：
        *   **操作**：在 `logwp\extras\petroplot\crossplot` 目录中，创建一个名为 `plot_profiles.py` 的文件。
        *   **操作**：在 `plot_profiles.py` 中，创建一个或多个 `PlotProfile` 对象，并使用 `registry.register` 函数将其注册到全局绘图服务中。
        *   **协作模式**: 我提供 `plot_profiles.py` 的完整代码，**由您负责创建文件并粘贴代码**。
        *   **目的**：提供默认的绘图样式。

### 7. 测试与验证 (3天)

*   **目标**：编写单元测试和集成测试，确保组件的正确性和稳定性。

*   **步骤**：

    1.  **编写单元测试 (2天)**：
        *   **操作**：为 `facade.py`, `internal\plotter.py`, `artifact_handler.py` 等模块编写单元测试。
        *   **协作模式**: 我提供所有单元测试的代码，**由您负责创建测试文件并应用**。
        *   **目的**：确保每个模块的逻辑正确。

    2.  **编写集成测试 (1天)**：
        *   **操作**：编写集成测试，测试 `run_crossplot_step` 和 `replot_crossplot_from_snapshot` 函数的完整流程。
        *   **协作模式**: 我提供集成测试的代码，**由您负责创建测试文件并应用**。
        *   **目的**：确保组件可以正确地与其他模块协同工作。

---

请注意，这只是一个初步的开发计划，具体的时间安排可能会根据实际情况进行调整。在开发过程中，请密切关注代码质量和可维护性，并遵循 SCAPE 机器学习组件开发最佳实践。

接下来，请告诉我您是否需要对这个开发计划进行修改或调整。
