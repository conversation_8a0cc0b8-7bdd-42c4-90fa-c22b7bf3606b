"""logwp.extras.petroplot.crossplot.plot_profiles - 交会图绘图模板

本模块负责定义并向全局注册表注册交会图组件所需的`PlotStyle`模板。

它遵循我们新的样式系统，为基于Plotly的交会图提供了一套标准化的、
可维护的默认外观。

通过在 `crossplot/__init__.py` 中导入此模块，可以实现模板的自动注册。

Architecture
------------
层次/依赖: petroplot/crossplot配置层，被plotting.registry使用
设计原则: 导入即注册、样式与逻辑分离、主题常量驱动
"""

from logwp.extras.plotting import PlotStyle, registry
from logwp.extras.plotting import theme_constants as tc

from .constants import CrossPlotProfiles


def _create_default_style() -> PlotStyle:
    """创建默认的交会图PlotStyle。

    此样式从全局的 theme_constants 中获取值，确保视觉一致性。
    """
    return PlotStyle(
        name=CrossPlotProfiles.DEFAULT.value,
        font={
            "family": tc.DEFAULT_FONT_FAMILY,
            "size_title": tc.FONT_SIZE_TITLE,
            "size_label": tc.FONT_SIZE_LABEL,
            "size_ticks": tc.FONT_SIZE_TICKS,
            "color": tc.TEXT_COLOR_LIGHT,
        },
        color={
            "cycle": tc.PLOTLY_COLOR_CYCLE,
            "background_canvas": tc.BACKGROUND_CANVAS_LIGHT,
            "background_plot": tc.BACKGROUND_PLOT_LIGHT,
            "grid_major": tc.GRID_MAJOR_LIGHT,
            "grid_minor": tc.GRID_MINOR_LIGHT,
            "tick_major": tc.FRAME_COLOR_LIGHT,
            "tick_minor": "#999999",
        },
        linewidth={
            "main": tc.LINEWIDTH_MAIN,
            "grid_major": tc.LINEWIDTH_GRID_MAJOR,
            "grid_minor": tc.LINEWIDTH_GRID_MINOR,
            "reference_line": tc.LINEWIDTH_REFERENCE,
            "tick_major": tc.LINEWIDTH_TICK_MAJOR,
            "tick_minor": tc.LINEWIDTH_TICK_MINOR,
        },
        tick={"major_length": 6, "minor_length": 4},
        marker={"default_size": 8, "default_alpha": 0.7, "default_symbol": "circle", "default_edgewidth": 0.5},
        frame={"color": tc.FRAME_COLOR_LIGHT, "width": tc.LINEWIDTH_FRAME},
        backend_specific={"plotly": {"template": "plotly_white"}},
    )

# 在模块加载时，立即执行注册
registry.register(_create_default_style())
