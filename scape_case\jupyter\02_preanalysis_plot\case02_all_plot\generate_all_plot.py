"""
各种交会图生成模块

将crossplot.ipynb转换为Python模块，包含NMR三角形图版和各种交会图的生成功能。
"""

from pathlib import Path
from typing import Tuple, Dict, Any

import numpy as np
import pandas as pd

# 导入 logwp 核心库
from logwp.io import WpExcelReader, WpExcelWriter
from logwp.models.well_project import WpWellProject
from logwp.models.datasets.bundle import WpDataFrameBundle
from logwp.models.constants import WpDepthRole

from logwp.extras.tracking import RunContext
from logwp.extras.plotting import registry as plot_registry

from logwp.extras.petroplot.common import *

from logwp.extras.petroplot.nmr_ternary import (
    create_publication_ready_perm_config as nmr_ternary_create_publication_ready_perm_config,
    run_nmr_ternary_plot_step,
    NmrTernaryDataSelectors
)

from logwp.extras.petroplot.crossplot import (
    create_publication_single_series_scatter_config as crossplot_create_publication_ready_config,
    run_crossplot_step,
    CrossPlotConfig
)


def setup_pandas_display() -> None:
    """设置pandas显示选项"""
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', None)
    pd.set_option('display.max_colwidth', 50)
    print("===库导入完成!")


def load_data() -> Tuple[WpDataFrameBundle, str, str, WpDataFrameBundle, str, str, WpDataFrameBundle, str, str]:
    """
    加载所有数据文件

    Returns:
        Tuple包含:
        - wl_bundle, wl_well_name, wl_depth_name
        - lwd_bundle, lwd_well_name, lwd_depth_name
        - crossplot_bundle, crossplot_well_name, crossplot_depth_name
    """
    reader = WpExcelReader()

    # --- 加载WL数据 ---
    wl_data_file_path = Path(__file__).parent / "nmr_ternary.wp.xlsx"
    wl_project = reader.read(wl_data_file_path)
    print(f"✅ 成功读取WL数据: {wl_data_file_path}")

    wl_bundle = wl_project.get_dataset("nmr_ternary").extract_curve_dataframe_bundle(
        include_system_columns=True
    )
    wl_well_name = wl_bundle.curve_metadata.get_well_identifier_curves()[0]
    wl_depth_name = wl_bundle.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)[0]

    print(f"===WL Bundle: {wl_bundle.data.head()}")

    # --- 加载LWD数据 ---
    lwd_data_file_path = Path(__file__).parent / "lwd_nmr_ternary.wp.xlsx"
    lwd_project = reader.read(lwd_data_file_path)
    print(f"✅ 成功读取LWD数据: {lwd_data_file_path}")

    lwd_bundle = lwd_project.get_dataset("lwd_nmr_ternary").extract_curve_dataframe_bundle(
        include_system_columns=True
    )
    lwd_well_name = lwd_bundle.curve_metadata.get_well_identifier_curves()[0]
    lwd_depth_name = lwd_bundle.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)[0]

    print(f"===LWD Bundle: {lwd_bundle.data.head()}")

    # --- 加载CrossPlot数据 ---
    crossplot_data_file_path = Path(__file__).parent / "crossplot.wp.xlsx"
    crossplot_project = reader.read(crossplot_data_file_path)
    print(f"✅ 成功读取crossplot数据: {crossplot_data_file_path}")

    crossplot_bundle = crossplot_project.get_dataset("crossplot").extract_curve_dataframe_bundle(
        include_system_columns=True
    )
    crossplot_well_name = crossplot_bundle.curve_metadata.get_well_identifier_curves()[0]
    crossplot_depth_name = crossplot_bundle.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)[0]

    print(f"===Crossplot Bundle: {crossplot_bundle.data.head()}")

    return (wl_bundle, wl_well_name, wl_depth_name,
            lwd_bundle, lwd_well_name, lwd_depth_name,
            crossplot_bundle, crossplot_well_name, crossplot_depth_name)


def initialize_run_context() -> RunContext:
    """初始化RunContext"""
    output_dir = Path(__file__).parent / "output01"
    run_dir_name = RunContext.generate_timestamped_run_name(prefix="crossplot")
    run_context = RunContext(output_dir / run_dir_name, overwrite=True)
    print(f"实验运行已初始化，所有产物将保存至: {run_context.run_dir.resolve()}")
    return run_context


def create_wl_nmr_ternary_plot(
    run_context: RunContext,
    wl_bundle: WpDataFrameBundle,
    wl_well_name: str,
    wl_depth_name: str
) -> Any:
    """创建WL NMR三角形图版"""
    plot_config, plot_profile = nmr_ternary_create_publication_ready_perm_config(
        enable_background=True,
        colorscale='Plasma',
        font_size_pt=20,
        tick_font_size_pt=20,
        marker_size=18,
        legend_position='right'
    )

    wl_selectors = NmrTernaryDataSelectors(
        macro_curve="VMACRO",
        micro_curve="VMICRO",
        meso_curve="VMESO",
        color_curve="K_LABEL",
        hover_extra_curves=[wl_well_name, wl_depth_name]
    )

    wl_results = run_nmr_ternary_plot_step(
        config=plot_config,
        selectors=wl_selectors,
        ctx=run_context,
        bundle=wl_bundle,
        plot_profile=plot_profile,
        prefix="WL"
    )

    return wl_results


def create_lwd_nmr_ternary_plot(
    run_context: RunContext,
    lwd_bundle: WpDataFrameBundle,
    lwd_well_name: str,
    lwd_depth_name: str
) -> Any:

    """创建WL NMR三角形图版"""
    plot_config, plot_profile = nmr_ternary_create_publication_ready_perm_config(
        enable_background=True,
        colorscale='Plasma',
        font_size_pt=20,
        tick_font_size_pt=20,
        marker_size=18,
        legend_position='right'
    )

    """创建LWD NMR三角形图版"""
    lwd_selectors = NmrTernaryDataSelectors(
        macro_curve="VMACRO_LWD",
        micro_curve="VMICRO_LWD",
        meso_curve="VMESO_LWD",
        color_curve="K_LABEL",
        hover_extra_curves=[lwd_well_name, lwd_depth_name]
    )

    lwd_results = run_nmr_ternary_plot_step(
        config=plot_config,
        selectors=lwd_selectors,
        ctx=run_context,
        bundle=lwd_bundle,
        plot_profile=plot_profile,
        prefix="LWD"
    )

    return lwd_results


def create_crossplot(
    run_context: RunContext,
    crossplot_bundle: WpDataFrameBundle
) -> Any:
    """创建各种交会图"""
    plot_config = crossplot_create_publication_ready_config(
        bundle_name="main",
        x_curve="RD_LWD",
        y_curve="RS_LWD",
        z_curve="K_LABEL",
        series_id="w/ K",
        marker_size=18,

        x_title="RD_LWD(ohm.m)",
        x_range_min=0.2,
        x_range_max=2000,
        x_log=True,

        y_title="RS_LWD(ohm.m)",
        y_range_min=0.2,
        y_range_max=2000,
        y_log=True,

        z_title="log(K)",
        z_cmin=-2.0,
        z_cmax=3.0,
        z_log_transform=True,
        z_cmap="Plasma",
        z_show_nan=True,
        z_nan_legend_text="w/o K",

        show_ref_line=True,
        show_marginals=False,

        font_size_pt=20,
        tick_font_size_pt=20,
        export_width_inches=4.2,
        export_height_inches=3.5,
        export_dpi=300,
    )

    results = run_crossplot_step(
        config=plot_config,
        ctx=run_context,
        bundles={
            "main": crossplot_bundle
        },
        prefix="rd_rs_lwd",
    )

    return results


def main() -> None:
    """主函数，执行完整的绘图流程"""
    # 设置pandas显示选项
    setup_pandas_display()

    # 加载数据
    (wl_bundle, wl_well_name, wl_depth_name,
     lwd_bundle, lwd_well_name, lwd_depth_name,
     crossplot_bundle, crossplot_well_name, crossplot_depth_name) = load_data()

    # 初始化RunContext
    run_context = initialize_run_context()

    try:
        # 创建WL NMR三角形图版
        print("开始创建WL NMR三角形图版...")
        #wl_results = create_wl_nmr_ternary_plot(
        #    run_context, wl_bundle, wl_well_name, wl_depth_name
        #)


        # 创建LWD NMR三角形图版
        print("开始创建LWD NMR三角形图版...")
        #lwd_results = create_lwd_nmr_ternary_plot(
        #    run_context, lwd_bundle, lwd_well_name, lwd_depth_name
        #)

        # 创建交会图
        print("开始创建交会图...")
        crossplot_results = create_crossplot(run_context, crossplot_bundle)

        print("所有图表创建完成!")

    finally:
        # 结束RunContext
        run_context.finalize()
        print("实验运行已完成并清理!")


if __name__ == "__main__":
    main()
