"""logwp.extras.petroplot.crossplot.presets - 交会图配置预设

本模块提供了一系列便捷函数，用于快速生成针对特定场景的 `CrossPlotConfig`
对象。这旨在简化用户操作，避免手动构建复杂的配置模型。

Architecture
------------
层次/依赖: petroplot/crossplot便捷方法层，被最终用户调用
设计原则: 用户友好、封装复杂性、提供最佳实践、样式与内容分离
"""

from typing import Optional, Tuple, Literal

import numpy as np

from logwp.extras.plotting import PlotStyle
from logwp.extras.plotting import registry
from .constants import CrossPlotProfiles
from .config import (
    AxisConfig,
    AxisRangeConfig,
    AxisTitleConfig,
    ColorbarConfig,
    CrossPlotConfig,
    ExportConfig,
    GridConfig,
    TickConfig,
    FigureConfig,
    MarkerConfig,
    ReferenceLineConfig,
    ScaleType,
    SeriesConfig,
    MarginalConfig,
    MarginalEnabled,
    MarginalKind,
)


def create_publication_single_series_scatter_config(
    *,
    # --- Data & Series ---
    bundle_name: str = "default",
    x_curve: str,
    y_curve: str,
    z_curve: Optional[str] = None,
    series_id: str = "Data",
    main_title: Optional[str] = None,
    marker_size: int = 8,

    # --- X-Axis ---
    x_title: str,
    x_range_min: float,
    x_range_max: float,
    x_log: bool = False,
    x_major_tick_interval: Optional[float] = None,
    x_minor_tick_interval: Optional[float] = None,
    x_show_major_grid: bool = True,
    x_show_minor_grid: bool = True,

    # --- Y-Axis ---
    y_title: str,
    y_range_min: float,
    y_range_max: float,
    y_log: bool = False,
    y_major_tick_interval: Optional[float] = None,
    y_minor_tick_interval: Optional[float] = None,
    y_show_major_grid: bool = True,
    y_show_minor_grid: bool = True,

    # --- Colorbar ---
    z_title: Optional[str] = None,
    z_cmin: Optional[float] = None,
    z_cmax: Optional[float] = None,
    z_log_transform: bool = False,
    z_cmap: str = "Plasma",
    z_show_nan: bool = False,
    z_nan_legend_text: Optional[str] = None,
    colorbar_position: Literal["right", "left", "top", "bottom"] = "right",

    # --- Features ---
    show_ref_line: bool = False,
    ref_line_color: Optional[str] = None,
    show_marginals: bool = False,
    marginal_kind: MarginalKind = MarginalKind.HIST,

    # --- Global Style & Export ---
    font_size_pt: int = 12,
    tick_font_size_pt: int = 10,
    export_width_inches: float = 4.2,
    export_height_inches: float = 3.5,
    export_dpi: int = 300,
) -> CrossPlotConfig:
    """
    创建一个适用于论文发表的、高度可定制的CrossPlotConfig。

    此预设专注于生成一个单系列的散点图，并提供了丰富的参数来精细控制
    图表的每一个方面，从数据选择到坐标轴、颜色轴、边缘图和导出设置。
    它内部封装了一套适用于出版物的高质量默认样式，同时允许用户通过
    参数进行覆盖。

    Args:
        bundle_name: 要使用的数据源的逻辑名称。
        x_curve: 用于X轴的逻辑曲线名。
        y_curve: 用于Y轴的逻辑曲线名。
        z_curve: (可选) 用于颜色映射的逻辑曲线名。
        series_id: 数据系列的标识符，用于图例。
        main_title: (可选) 图表的主标题。
        marker_size: 散点图中标记的大小。

        x_title: X轴的标题。
        x_range_min: X轴的最小值。
        x_range_max: X轴的最大值。
        x_log: 如果为True，将X轴设置为对数刻度。
        x_major_tick_interval: (可选) X轴主刻度的间隔。
        x_minor_tick_interval: (可选) X轴次要刻度的间隔。
        x_show_major_grid: 是否显示X轴主网格线。
        x_show_minor_grid: 是否显示X轴次要网格线。

        y_title: Y轴的标题。
        y_range_min: Y轴的最小值。
        y_range_max: Y轴的最大值。
        y_log: 如果为True，将Y轴设置为对数刻度。
        y_major_tick_interval: (可选) Y轴主刻度的间隔。
        y_minor_tick_interval: (可选) Y轴次要刻度的间隔。
        y_show_major_grid: 是否显示Y轴主网格线。
        y_show_minor_grid: 是否显示Y轴次要网格线。

        z_title: (可选) 颜色条的标题。如果为None且z_curve存在，则默认为z_curve的名称。
        z_cmin: (可选) 颜色条的最小值。
        z_cmax: (可选) 颜色条的最大值。
        z_log_transform: 是否对颜色轴数据进行对数变换。
        z_cmap: 颜色映射表名称。
        z_show_nan: 是否为NaN值指定特殊颜色（灰色）。
        z_nan_legend_text: (可选) NaN值的图例文字。如果设置，将显示NaN图例。
        colorbar_position: 颜色条的位置 ('right', 'left', 'top', 'bottom')。

        show_ref_line: 是否显示对角参考线。
        ref_line_color: (可选) 参考线的颜色。
        show_marginals: 是否显示边缘图。
        marginal_kind: 边缘图的类型。

        font_size_pt: 主要字体大小（如坐标轴标签）。
        tick_font_size_pt: 刻度标签字体大小。
        export_width_inches: 导出图像的宽度（英寸）。
        export_height_inches: 导出图像的高度（英寸）。
        export_dpi: 导出图像的分辨率（DPI）。

    Returns:
        一个完全配置好的、包含内容和样式的CrossPlotConfig对象。
    """
    # 1. Get the default style from the registry and apply user overrides
    base_style = registry.get(CrossPlotProfiles.DEFAULT.value, expected_type=PlotStyle)

    # Create a style object with overrides based on function parameters
    override_style = PlotStyle(
        font={
            "size_label": font_size_pt,
            "size_ticks": tick_font_size_pt,
        },
        color={
            "grid_major": "#555555",  # Dark Gray
            "grid_minor": "#aaaaaa",  # Gray
        },
        frame={
            "color": "#000000",  # Black
            "width": 1.0,
        },
        marker={"default_size": marker_size},
    )
    # Deep merge the overrides with the base style
    publication_style = override_style.merge_with_base(base_style)

    # 2. Define the data series
    series_list = [
        SeriesConfig(
            id=series_id,
            bundle_name=bundle_name,
            x_curve=x_curve,
            y_curve=y_curve,
            z_curve=z_curve,
            marker=MarkerConfig(size=marker_size,linecolor="black"),
        )
    ]

    # 智能设置次要刻度，对数轴默认显示所有网格线
    x_minor_enabled = (x_log and x_show_minor_grid) or (x_minor_tick_interval is not None)
    x_actual_minor_interval = 'L1' if x_log else x_minor_tick_interval

    y_minor_enabled = (y_log and y_show_minor_grid) or (y_minor_tick_interval is not None)
    y_actual_minor_interval = 'L1' if y_log else y_minor_tick_interval

    # 3. Build axis configurations
    xaxis_config = AxisConfig(
        title=AxisTitleConfig(text=x_title),
        scale=ScaleType.LOG if x_log else ScaleType.LINEAR,
        range=AxisRangeConfig(
            min_value=np.log10(x_range_min) if x_log else x_range_min,
            max_value=np.log10(x_range_max) if x_log else x_range_max

        ),
        ticks=TickConfig(
            interval=x_major_tick_interval,
            minor_enabled=x_minor_enabled,
            minor_interval=x_actual_minor_interval,
        ),
        grid=GridConfig(major_enabled=x_show_major_grid, minor_enabled=x_show_minor_grid),
    )

    yaxis_config = AxisConfig(
        title=AxisTitleConfig(text=y_title),
        scale=ScaleType.LOG if y_log else ScaleType.LINEAR,
        range=AxisRangeConfig(
            min_value=np.log10(y_range_min) if y_log else y_range_min,
            max_value=np.log10(y_range_max) if y_log else y_range_max
        ),
        ticks=TickConfig(
            interval=y_major_tick_interval,
            minor_enabled=y_minor_enabled,
            minor_interval=y_actual_minor_interval,
        ),
        grid=GridConfig(major_enabled=y_show_major_grid, minor_enabled=y_show_minor_grid),
    )

    # 4. Build colorbar configuration
    colorbar_pos_map = {
        "right": {"orientation": "vertical", "x": 1.02, "y": 0.5, "xanchor": "left", "yanchor": "middle"},
        "left": {"orientation": "vertical", "x": -0.15, "y": 0.5, "xanchor": "right", "yanchor": "middle"},
        "top": {"orientation": "horizontal", "x": 0.5, "y": 1.02, "xanchor": "center", "yanchor": "bottom"},
        "bottom": {"orientation": "horizontal", "x": 0.5, "y": -0.15, "xanchor": "center", "yanchor": "top"},
    }
    colorbar_pos_kwargs = colorbar_pos_map.get(colorbar_position, colorbar_pos_map["right"])

    colorbar_config = ColorbarConfig(
        visible=z_curve is not None,
        log_transform=z_log_transform,
        title=z_title or z_curve or "",
        cmin=z_cmin,
        cmax=z_cmax,
        cmap=z_cmap,
        nan_color="#cccccc" if z_show_nan else None,
        nan_show_legend=z_nan_legend_text is not None,
        nan_legend_text=z_nan_legend_text,
        len = 0.6,
        **colorbar_pos_kwargs,
    )

    # 5. Build other feature configurations
    marginal_config = MarginalConfig(
        enabled=MarginalEnabled.BOTH if show_marginals else MarginalEnabled.NONE,
        kind=marginal_kind,
        size_x=0.1,
        size_y=0.1,
    )

    ref_line_config = ReferenceLineConfig(
        visible=show_ref_line,
        color=ref_line_color,
    )

    # 6. Build the final CrossPlotConfig object
    config = CrossPlotConfig(
        figure=FigureConfig(
            title=main_title or "Cross-Plot",
            size=(int(export_width_inches * export_dpi), int(export_height_inches * export_dpi)),
        ),
        xaxis=xaxis_config,
        yaxis=yaxis_config,
        series=series_list,
        colorbar=colorbar_config,
        marginal=marginal_config,
        reference_line=ref_line_config,
        export=ExportConfig(width_inches=export_width_inches, height_inches=export_height_inches, dpi=export_dpi),
        style=publication_style,
    )

    return config
