{"config_type": "PlotProfile", "name": "validation.perm_corr.permeability_crossplot", "save_config": null, "rc_params": {}, "figure_props": {}, "title_props": {"fontsize": 14, "fontweight": "bold", "pad": 15}, "label_props": {"xlabel": "True Permeability (mD) - from right_bundle", "ylabel": "Predicted Permeability (mD) - from left_bundle", "fontsize": 12}, "artist_props": {"scatter": {"alpha": 0.7, "s": 60, "edgecolor": "white", "linewidth": 0.5, "label": "Data Points"}, "line_1to1": {"color": "red", "linestyle": "-", "linewidth": 2.0, "label": "1:1 Line"}, "line_3x": {"color": "black", "linestyle": "--", "linewidth": 1.5, "label": "3x <PERSON><PERSON><PERSON>"}, "line_5x": {"color": "blue", "linestyle": "--", "linewidth": 1.5, "label": "5x <PERSON><PERSON><PERSON>"}, "line_10x": {"color": "green", "linestyle": "--", "linewidth": 1.5, "label": "10x E<PERSON><PERSON>"}, "text_box": {"fontsize": 10, "bbox": {"boxstyle": "round,pad=0.5", "facecolor": "aliceblue", "alpha": 0.8}}, "legend": {"loc": "lower right", "fontsize": 10}, "axes": {"xscale": "log", "yscale": "log", "aspect": "equal", "adjustable": "box", "xlim": [0.01, 1000], "ylim": [0.01, 1000]}, "grid": {"visible": true, "which": "both", "linestyle": "--", "linewidth": 0.5}}, "is_frozen": true}