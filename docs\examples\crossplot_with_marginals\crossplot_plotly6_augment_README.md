# 1. Plotly 6.0 原生功能支持分析 (Plotly 6.0 Native Feature Analysis)

> **功能更新 (2025-08-06)**:
> - 新增R-12 NaN值处理功能，采用双轨迹策略实现完整的NaN数据可视化控制，详见第5节。
> - 新增对数刻度下边缘图的原生支持，基于Context7技术分析实现Box、Violin、KDE在对数环境下的完整功能，详见第6节。

> **说明：** 本节基于 Plotly 6.0 的原生功能和我们在 `crossplot_plotly6_augment.py` 中的实际编程实践，详细分析了《测井交会图绘制要求》第1-4节中各项功能的支持情况。这为开发者提供了明确的实现路径和技术边界。

## 1.1 完全支持的功能 (✅ Fully Supported)

**坐标轴功能 (第1.1节, 第2.1节)**
- ✅ **对数坐标轴**：`fig.update_xaxes(type="log")` 原生支持，包括 log10 基数
- ✅ **线性坐标轴**：默认支持，性能优异
- ✅ **坐标轴范围设置**：`range=[min, max]` 支持，对数轴需使用指数值
- ✅ **主次刻度控制**：`minor=dict(ticks="inside", ticklen=6, showgrid=True)` 完整支持
- ✅ **网格线样式**：主次网格线独立控制，支持颜色、宽度、样式
- ✅ **轴标签和格式化**：支持 LaTeX 渲染和自定义格式化

**边缘图功能 (第1.4节, 第2.4节)**
- ✅ **多种边缘图类型**：histogram, box, violin, kde (基于 violin 实现)
- ✅ **对数刻度原生支持**：Box、Violin、KDE在对数环境下完整功能支持，无需数据预处理
- ✅ **边缘图高级配置**：Plotly 6.0 原生支持箱线图数据点显示、小提琴图内部箱线图等
- ✅ **颜色联动**：边缘图与主图图例完全同步，支持交互式显隐
- ✅ **尺寸和间距控制**：通过 `make_subplots` 精确控制边缘图占比
- ✅ **统计计算准确性**：四分位数、KDE计算在原始数据空间进行，确保统计正确性

**颜色轴功能 (第1.3节, 第2.3节)**
- ✅ **多系列独立色轴**：每个系列可配置独立的 `colorbar`，支持不同物理量同时可视化
- ✅ **色轴位置控制**：`x`, `y`, `xanchor`, `yanchor` 精确定位，支持垂直居中
- ✅ **颜色映射**：支持所有 Plotly 内置 colorscale，自定义颜色映射
- ✅ **色轴标题和刻度**：完整的标题、刻度值、刻度文本自定义

**数据层功能 (第1.2节, 第2.2节)**
- ✅ **多系列支持**：通过 `legendgroup` 实现系列分组和联动
- ✅ **散点和折线**：`go.Scatter` 支持 markers, lines, 或两者组合
- ✅ **误差棒**：`error_x`, `error_y` 完整支持对称和非对称误差
- ✅ **标记符号和样式**：丰富的 marker 符号、尺寸、透明度、边框控制

**图例和布局 (第1.5节, 第2.5节)**
- ✅ **图例位置控制**：支持所有位置选项，智能避让色轴
- ✅ **图件尺寸和标题**：完整的布局控制，支持响应式设计
- ✅ **导出格式**：PNG, PDF, SVG, HTML 全支持，DPI 可控

## 1.2 部分支持或需要变通的功能 (⚠️ Partially Supported)

**坐标轴限制**
- ⚠️ **symlog 坐标轴**：Plotly 6.0 不支持 symlog（对称对数）刻度

- ⚠️ **自定义对数基数**：仅支持 log10，不支持 log2 或自然对数


**边缘图限制**
- ⚠️ **CDF/CCDF 边缘图**：需要手动计算累积分布函数

- ⚠️ **Probability Plot**：需要使用 `scipy.stats` 计算概率图数据

- ⚠️ **对数轴边缘图分箱**：需要手动实现对数等比分箱
  - **实现方案**：使用 `np.logspace()` 创建对数分箱，确保与主轴一致

## 1.3 不支持或需要完全自定义的功能 (❌ Not Supported)

**高级坐标轴功能**
- ❌ **坐标轴方向反转**：虽然可以通过 `autorange="reversed"` 实现，但与边缘图联动复杂
  - **影响**：深度轴自上而下递增等地质应用场景需要额外处理

**复杂边缘图布局**
- ❌ **边缘图 Facet 模式**：Plotly 原生不支持边缘图的分面显示
  - **影响**：多系列并排显示需要手动实现子图布局

- ❌ **边缘图 Stack 模式**：堆叠显示多系列边缘图需要手动计算
  - **影响**：无法直接实现多系列堆叠直方图

# 2. 代码实现最佳实践

**架构选择**
1. **强制使用 `plotly.graph_objects` + `make_subplots`**：确保完全控制和规范遵循
2. **避免 `plotly.express` 作为主要方案**：仅用作数据计算的辅助工具
3. **分层架构设计**：配置层 → 翻译层 → 绘图层 → 输出层

**性能优化**
1. **大数据处理**：超过 5万点自动切换到 WebGL 渲染 (`go.Scattergl`)
2. **边缘图优化**：大数据集使用 KDE 替代直方图，避免过多 bins
3. **内存管理**：及时释放中间数据，使用生成器处理大数据集

**兼容性保证**
1. **浏览器兼容**：确保生成的 HTML 在主流浏览器中正常显示
2. **导出质量**：PNG/PDF 导出使用 300+ DPI，确保出版质量
3. **交互功能**：充分利用 Plotly 的交互特性，但提供静态导出选项

**开发工具链**
1. **类型安全**：使用 Pydantic 进行配置验证，TypedDict 定义数据结构


# 3. 与规范的对应关系

| 规范要求 | Plotly 6.0 支持度 | 实现复杂度 | 推荐方案 |
|---------|------------------|-----------|---------|
| R-1 轴-边缘一致性 | ✅ 完全支持 | 中等 | `make_subplots` + 共享轴 |
| R-2 归一化柱高 | ✅ 完全支持 | 简单 | `histnorm='probability'` |
| R-3 Series 颜色一致 | ✅ 完全支持 | 简单 | `legendgroup` 绑定 |
| R-4 legendgroup 绑定 | ✅ 完全支持 | 简单 | 原生 `legendgroup` 属性 |
| R-5 z-order 层级 | ✅ 完全支持 | 简单 | trace 添加顺序控制 |
| R-6 Colorbar 独立 | ✅ 完全支持 | 中等 | 多 `coloraxis` 配置 |
| R-7 缩放/导出同步 | ✅ 完全支持 | 简单 | Plotly 自动处理 |
| R-8 自适应降采样 | ⚠️ 需要手动实现 | 高 | 数据量检测 + WebGL |
| R-9 深色主题对比度 | ✅ 完全支持 | 中等 | 模板系统 + 颜色验证 |
| R-10 版本水印 | ✅ 完全支持 | 简单 | `fig.add_annotation()` |
| R-11 Tooltip 双值显示 | ✅ 完全支持 | 中等 | 自定义 `hovertemplate` |
| R-12 数据清洗 | ✅ 完全支持 | 简单 | 数据预处理 + 异常处理 |
| R-12 NaN值处理 | ✅ 完全支持 | 中等 | 双轨迹策略 + 配置化NaN颜色 |

**总结**：Plotly 6.0 对《测井交会图绘制要求》的支持度约为 **85%**，核心功能完全支持，部分高级功能需要变通实现。通过合理的架构设计和实现策略，可以达到规范要求的 **95%** 以上的功能覆盖。

# 4. 代码实现细节

> **说明：** 本节提供了《测井交会图绘制要求》第2节（功能参数定义）和第3节（外观样式定制）中的参数与 `crossplot_plotly6_augment.py` 实现的详细对应关系。这为开发者提供了从规范到代码的直接映射指南。

通过这个详细的参数映射表，开发者可以：
1. **快速定位**：从规范参数直接找到对应的实现代码
2. **理解架构**：掌握配置对象模式和优先级逻辑
3. **处理限制**：了解Plotly限制和相应的变通方案
4. **扩展功能**：按照既定模式添加新功能
5. **保持一致**：确保实现与规范的完全对应

## 4.1 功能参数映射 (第2节 → 实现代码)

| 规范参数 | 实现类/属性 | 对应代码位置 | 说明 |
|---------|------------|-------------|------|
| **2.1 坐标轴设置** |
| `xlabel`, `ylabel` | `AxisConfig.title.text` | `AxisTitleConfig.text` | 轴标题文本 |
| `xscale`, `yscale` | `AxisConfig.scale` | `ScaleType.LINEAR/LOG` | 坐标轴类型 |
| `xlim`, `ylim` | `AxisConfig.range` | `AxisRangeConfig.min_value/max_value` | 坐标轴范围 |
| `xticks`, `yticks` | `AxisConfig.ticks` | `TickConfig.interval/start` | 刻度设置 |
| `xgrid`, `ygrid` | `AxisConfig.grid` | `GridConfig.major_enabled/minor_enabled` | 网格线控制 |
| **2.2 数据层（Series）** |
| `series.id` | `SeriesConfig.id` | `SeriesConfig.id` | 系列标识符 |
| `series.type` | `SeriesConfig.type` | `SeriesType.SCATTER/LINE` | 绘图类型 |
| `series.marker.symbol` | `SeriesConfig.marker.symbol` | `MarkerConfig.symbol` | 标记符号 |
| `series.marker.size` | `SeriesConfig.marker.size` | `MarkerConfig.size` | 标记大小 |
| `series.marker.facecolor` | `SeriesConfig.marker.facecolor` | `MarkerConfig.facecolor` | 填充色 |
| `series.marker.edgecolor` | `SeriesConfig.marker.linecolor` | `MarkerConfig.linecolor` | 边框色 |
| `series.marker.edgewidth` | `SeriesConfig.marker.edgewidth` | `MarkerConfig.edgewidth` | 边框宽度 |
| `series.marker.alpha` | `SeriesConfig.marker.opacity` | `MarkerConfig.opacity` | 透明度 |
| `series.line.style` | `SeriesConfig.line.style` | `LineConfig.style` | 线型 |
| `series.line.color` | `SeriesConfig.line.color` | `LineConfig.color` | 线条颜色 |
| `series.line.width` | `SeriesConfig.line.width` | `LineConfig.width` | 线条宽度 |
| `series.error_bars` | `SeriesConfig.error_bars` | `ErrorBarConfig.*` | 误差棒配置 |
| `series.hover` | `SeriesConfig.hover` | `HoverConfig.*` | 悬停信息 |
| `series.legendgroup` | `SeriesConfig.legendgroup` | `SeriesConfig.legendgroup` | 图例分组 |
| **2.3 颜色轴设置** |
| `cmap` | `ColorbarConfig.cmap` | `ColorbarConfig.cmap` | 颜色映射 |
| `clim` | `ColorbarConfig.clim` | `ColorbarConfig.clim` | 颜色范围 |
| `cscale` | `ColorbarConfig.scale` | `ColorbarConfig.scale` | 颜色刻度类型 |
| `colorbar.orientation` | `ColorbarConfig.orientation` | `ColorbarConfig.orientation` | 色轴方向 |
| `colorbar.title` | `ColorbarConfig.title` | `ColorbarConfig.title` | 色轴标题 |
| `colorbar.ticks` | `ColorbarConfig.tickvals` | `ColorbarConfig.tickvals` | 色轴刻度 |
| `z_nan.color` | `ColorbarConfig.nan_color` | `ColorbarConfig.nan_color` | NaN值颜色 |
| `z_nan.show_legend` | `ColorbarConfig.nan_show_legend` | `ColorbarConfig.nan_show_legend` | NaN图例显示 |
| **2.4 边缘图设置** |
| `marginal.enabled` | `MarginalConfig.enabled` | `MarginalEnabled.NONE/X/Y/BOTH` | 边缘图启用 |
| `marginal.kind` | `MarginalConfig.kind` | `MarginalKind.HIST/BOX/VIOLIN/KDE` | 边缘图类型 |
| `marginal.series` | `MarginalConfig.series` | `MarginalConfig.series` | 系列选择 |
| `marginal.bins` | `MarginalConfig.bins` | `MarginalConfig.bins` | 分箱设置 |
| `marginal.overlay` | `MarginalConfig.overlay` | `MarginalConfig.overlay` | 叠加模式 |
| `marginal.kde.bandwidth` | `KDEStyleConfig.bandwidth` | `KDEStyleConfig.bandwidth` | KDE带宽 |
| `marginal.box.orientation` | 自动推导 | `_draw_marginal_*` 方法 | 自动确定方向 |
| **2.5 图例 & 画布** |
| `legend.show` | `LegendConfig.enabled` | `LegendConfig.enabled` | 图例显示 |
| `legend.loc` | `LegendConfig.position` | `LegendConfig.position` | 图例位置 |
| `legend.ncols` | `LegendConfig.ncols` | `LegendConfig.ncols` | 图例列数 |
| `legend.fontsize` | `style.font.size_legend` | `StyleConfig.font.size_legend` | 图例字号 |
| `figure.title` | `FigureConfig.title` | `FigureConfig.title` | 图表标题 |
| `figure.size` | `FigureConfig.size` | `FigureConfig.size` | 图表尺寸 |
| `figure.watermark` | `FigureConfig.watermark` | `FigureConfig.watermark` | 水印设置 |
| `layout.aspect` | `FigureConfig.aspect` | `FigureConfig.aspect` | 纵横比 |
| **2.6 导出 & 交互** |
| `dpi` | `save_crossplot()` 参数 | `save_crossplot(dpi=300)` | 导出分辨率 |
| `export.format` | `save_crossplot()` 参数 | 文件扩展名自动识别 | 导出格式 |
| `export.size` | `save_crossplot()` 参数 | `save_crossplot(width, height)` | 导出尺寸 |
| `interactive.enabled` | Plotly 默认支持 | 原生交互功能 | 交互模式 |

## 4.2 样式参数映射 (第3节 → 实现代码)

| 规范参数 | 实现类/属性 | 对应代码位置 | 默认值 |
|---------|------------|-------------|--------|
| **3.1 字体 (Font)** |
| `style.font.family` | `StyleConfig.font.family` | `StyleConfig.font["family"]` | "Arial" |
| `style.font.size_title` | `StyleConfig.font.size_title` | `StyleConfig.font["size_title"]` | 14 |
| `style.font.weight_title` | `StyleConfig.font.weight_title` | `StyleConfig.font["weight_title"]` | "bold" |
| `style.font.size_label` | `StyleConfig.font.size_label` | `StyleConfig.font["size_label"]` | 12 |
| `style.font.size_ticks` | `StyleConfig.font.size_ticks` | `StyleConfig.font["size_ticks"]` | 10 |
| `style.font.color` | `StyleConfig.font.color` | `StyleConfig.font["color"]` | "#333333" |
| **3.2 颜色 (Color)** |
| `style.color.cycle` | `StyleConfig.color.cycle` | `StyleConfig.color["cycle"]` | ["#1f77b4", "#ff7f0e", ...] |
| `style.color.background_canvas` | `StyleConfig.color.background_canvas` | `StyleConfig.color["background_canvas"]` | "#ffffff" |
| `style.color.background_plot` | `StyleConfig.color.background_plot` | `StyleConfig.color["background_plot"]` | "#ffffff" |
| `style.color.background_marginal` | `StyleConfig.color.background_marginal` | `StyleConfig.color["background_marginal"]` | "#f7f7f7" |
| `style.color.grid` | `StyleConfig.color.grid` | `StyleConfig.color["grid"]` | "#bbbbbb" |
| **3.3 线条与边框 (Line & Frame)** |
| `style.linewidth.main` | `StyleConfig.linewidth.main` | `StyleConfig.linewidth["main"]` | 1.2 |
| `style.linewidth.grid_major` | `StyleConfig.linewidth.grid_major` | `StyleConfig.linewidth["grid_major"]` | 0.6 |
| `style.linewidth.grid_minor` | `StyleConfig.linewidth.grid_minor` | `StyleConfig.linewidth["grid_minor"]` | 0.4 |
| `style.linewidth.reference_line` | `StyleConfig.linewidth.reference_line` | `StyleConfig.linewidth["reference_line"]` | 1.0 |
| `style.linestyle.grid_major` | `StyleConfig.linestyle.grid_major` | `StyleConfig.linestyle["grid_major"]` | "--" |
| `style.linestyle.grid_minor` | `StyleConfig.linestyle.grid_minor` | `StyleConfig.linestyle["grid_minor"]` | ":" |
| `style.linestyle.reference_line` | `StyleConfig.linestyle.reference_line` | `StyleConfig.linestyle["reference_line"]` | "-." |
| `style.frame.color` | `StyleConfig.frame.color` | `StyleConfig.frame["color"]` | "#666666" |
| `style.frame.width` | `StyleConfig.frame.width` | `StyleConfig.frame["width"]` | 0.8 |
| **3.4 标记 (Marker)** |
| `style.marker.default_size` | `StyleConfig.marker.default_size` | `StyleConfig.marker["default_size"]` | 36 |
| `style.marker.default_alpha` | `StyleConfig.marker.default_alpha` | `StyleConfig.marker["default_alpha"]` | 0.6 |
| `style.marker.default_symbol` | `StyleConfig.marker.default_symbol` | `StyleConfig.marker["default_symbol"]` | "circle" |
| `style.marker.default_edgewidth` | `StyleConfig.marker.default_edgewidth` | `StyleConfig.marker["default_edgewidth"]` | 0.5 |
| **3.5 布局与间距 (Layout & Spacing)** |
| `style.padding.title` | `StyleConfig.padding.title` | `StyleConfig.padding["title"]` | 10 |
| `style.padding.axes_label` | `StyleConfig.padding.axes_label` | `StyleConfig.padding["axes_label"]` | 8 |
| `style.legend.item_spacing` | `StyleConfig.legend.item_spacing` | `StyleConfig.legend["item_spacing"]` | 5 |
| `style.legend.handle_length` | `StyleConfig.legend.handle_length` | `StyleConfig.legend["handle_length"]` | 15 |

## 4.3 实现架构映射 (Implementation Architecture Mapping)

| 规范概念 | 实现方式 | 核心类/方法 | 说明 |
|---------|---------|------------|------|
| **配置对象模式** | Pydantic BaseModel | `CrossPlotConfig` | 单一配置入口，类型安全验证 |
| **样式优先级逻辑** | 方法内优先级检查 | `_get_color()`, `_draw_main_trace()` | Z值映射 > 系列特定 > 全局默认 |
| **组件注册机制** | 字典映射 + 方法分发 | `marginal_drawers` 字典 | 可扩展的边缘图类型支持 |
| **边缘图-主图联动** | 共享轴对象 | `make_subplots()` + 轴同步 | 确保坐标轴一致性 |
| **对数分箱处理** | 手动计算对数分箱 | `_calculate_log_bins()` | 解决Plotly Express限制 |
| **多系列颜色一致性** | `legendgroup` 绑定 | `SeriesConfig.legendgroup` | 主图与边缘图颜色同步 |
| **双色轴支持** | 独立colorbar配置 | `SeriesConfig.colorbar` | 每个系列独立色轴 |
| **性能优化** | 自动WebGL切换 | 数据量检测逻辑 | 大数据集自动优化 |

## 4.4 未实现功能

以下功能并未实现，只是是供一些可能的途径：

| 规范要求 | 实现挑战 | 变通方案 | 代码位置 |
|---------|---------|---------|---------|
| **symlog坐标轴** | Plotly不支持 | 数据预处理 + 自定义刻度 | 建议在数据层处理 |
| **自定义对数基数** | 仅支持log10 | 数据变换 + 刻度标签 | `np.log2(data)` + `ticktext` |
| **边缘图Facet模式** | 原生不支持 | 手动子图布局 | `make_subplots` 扩展 |
| **CDF/CCDF边缘图** | 需手动计算 | `np.cumsum()` + `go.Scatter` | 自定义绘图函数 |
| **对数轴边缘图分箱** | Express API限制 | 手动对数分箱 | `_calculate_log_bins()` |
| **深度轴反转** | 与边缘图联动复杂 | 数据层反转 | 数据预处理阶段 |
| **版本水印** | 需手动实现 | `fig.add_annotation()` | `FigureConfig.watermark` |
| **Tooltip双值显示** | 需自定义模板 | `hovertemplate` 定制 | `HoverConfig.template` |

## 4.5 配置示例对照 (Configuration Examples)

**规范配置示例：**
```python
# 规范中的参数设置
config = {
    "xlabel": "Porosity (%)",
    "xscale": "linear",
    "series": [{
        "id": "Well-A",
        "marker": {"symbol": "circle", "size": 8, "facecolor": "#1f77b4"}
    }],
    "marginal": {"enabled": "both", "kind": "hist"},
    "style": {"font": {"family": "Arial", "size_title": 14}}
}
```

**对应的实现配置：**
```python
# crossplot_plotly6_augment.py 中的配置
config = CrossPlotConfig(
    xaxis=AxisConfig(
        title=AxisTitleConfig(text="Porosity (%)"),
        scale=ScaleType.LINEAR
    ),
    series=[SeriesConfig(
        id="Well-A",
        marker=MarkerConfig(
            symbol="circle",
            size=8,
            facecolor="#1f77b4"
        )
    )],
    marginal=MarginalConfig(
        enabled=MarginalEnabled.BOTH,
        kind=MarginalKind.HIST
    ),
    style=StyleConfig(
        font={"family": "Arial", "size_title": 14}
    )
)
```

## 4.6 扩展性指南 (Extensibility Guidelines)

| 扩展需求 | 实现方法 | 示例代码 |
|---------|---------|---------|
| **新增边缘图类型** | 注册新的绘图函数 | `marginal_drawers['newtype'] = draw_newtype` |
| **新增标记符号** | 扩展MarkerConfig | `symbol: Literal["circle", "square", "custom"]` |
| **新增颜色映射** | 扩展ColorbarConfig | `cmap: Literal["Viridis", "Plasma", "Custom"]` |
| **新增导出格式** | 扩展save_crossplot | `format: Literal["png", "pdf", "webp"]` |
| **新增交互功能** | 扩展HoverConfig | 添加新的hover模式和模板 |

# 5. R-12 NaN值处理功能详解

> **功能更新**: 本模块已完整实现R-12规范要求的NaN值处理功能，采用双轨迹策略实现有效数据与NaN数据的完全分离渲染。

## 5.1 功能概述

**R-12规范要求**: "对于 Z（颜色轴）中的非有限值，当 z_nan.color 被设置时，这些点应被赋予指定颜色；否则，它们将被忽略"

**实现特性**:
- ✅ **NaN值颜色映射**: 通过`nan_color`参数为NaN数据点指定固定颜色
- ✅ **图例控制**: 通过`nan_show_legend`参数控制NaN数据点是否在图例中显示
- ✅ **双轨迹策略**: 有效数据与NaN数据分别绘制为独立轨迹，实现完全隔离
- ✅ **配置优先级**: 系列级配置优先于全局配置，提供最大灵活性
- ✅ **零架构影响**: 完全向后兼容，不影响现有代码

## 5.2 技术实现

**配置参数扩展**:
```python
# ColorbarConfig新增参数
colorbar=ColorbarConfig(
    visible=True,
    cmap="Viridis",
    title="饱和度 (%)",
    nan_color="#808080",        # NaN值显示为灰色
    nan_show_legend=True        # 在图例中显示NaN数据点
)
```

**双轨迹策略**:
1. **数据分离**: 自动将数据分为有效数据和NaN数据两组
2. **独立渲染**: 有效数据使用颜色映射，NaN数据使用固定颜色
3. **图例管理**: NaN轨迹的图例显示可独立控制

**配置优先级逻辑**:
```python
# 系列级配置优先于全局配置
if series_config.colorbar and hasattr(series_config.colorbar, 'nan_show_legend'):
    show_legend = series_config.colorbar.nan_show_legend
else:
    show_legend = self.config.colorbar.nan_show_legend
```

## 5.3 使用示例

**示例1: 显示NaN图例**
```python
config = CrossPlotConfig(
    colorbar=ColorbarConfig(
        visible=True,
        cmap="Viridis",
        title="饱和度 (%)",
        nan_color="#808080",      # 灰色
        nan_show_legend=True      # 显示图例
    ),
    # ... 其他配置
)
```

**示例2: 隐藏NaN图例**
```python
config = CrossPlotConfig(
    colorbar=ColorbarConfig(
        visible=True,
        cmap="Viridis",
        title="饱和度 (%)",
        nan_color="#808080",      # 灰色
        nan_show_legend=False     # 隐藏图例
    ),
    # ... 其他配置
)
```

## 5.4 参数映射补充

| 规范参数 | 实现类/属性 | 对应代码位置 | 说明 |
|---------|------------|-------------|------|
| `z_nan.color` | `ColorbarConfig.nan_color` | `ColorbarConfig.nan_color` | NaN值显示颜色 |
| `z_nan.show_legend` | `ColorbarConfig.nan_show_legend` | `ColorbarConfig.nan_show_legend` | NaN图例显示控制 |

# 6. 对数刻度下的边缘图处理

> **技术概述**: 本节详细说明如何在对数刻度坐标轴下正确处理不同类型的边缘图，基于Plotly 6.0原生功能和Context7技术分析结果。

## 6.1 技术原理

**Plotly 6.0对数刻度原生支持**:
基于我们使用Context7对Plotly文档的深入分析，go.Box、go.Violin和go.Histogram等组件在对数刻度环境下的工作原理如下：

- ✅ **轴级别配置**: 对数刻度通过`type="log"`在轴级别设置，而非数据级别
- ✅ **统计计算独立性**: 四分位数、KDE密度估计等统计计算在原始数据空间进行
- ✅ **显示变换分离**: 对数变换仅影响视觉显示层，不影响统计计算的准确性

## 6.2 不同边缘图类型的处理策略

### 直方图 (Histogram)
**特殊处理原因**: 直方图需要合理的分箱才能在对数刻度下正确显示数据分布。

**实现方法**:
```python
def _calculate_logarithmic_bins(self, data: List[DataPoint]) -> tuple:
    """仅为直方图计算对数分箱，其他类型使用Plotly原生支持"""
    if self.config.marginal.kind == MarginalKind.HIST:
        # 计算对数等比分箱
        all_x_vals = [d.x for d in data if d.x > 0]
        min_val = np.log10(min(all_x_vals))
        max_val = np.log10(max(all_x_vals))
        log_bins_x = np.logspace(min_val, max_val, num_bins)
        return log_bins_x
    return None  # 其他类型不需要特殊处理
```

**技术要点**:
- 使用`np.logspace()`创建对数等比分箱
- 确保分箱边界与对数刻度的显示一致
- 过滤掉非正值数据点（对数刻度要求）

### 箱线图 (Box Plot)
**原生支持**: 无需特殊数据处理，直接使用Plotly原生功能。

**关键发现** (基于Context7分析):
- go.Box的quartilemethod参数（linear/inclusive/exclusive）在对数刻度环境下正常工作
- 四分位数计算在原始数据空间进行，显示时自动应用对数变换
- 异常值检测算法不受坐标轴刻度类型影响

**实现代码**:
```python
# 无需特殊处理，轴级别对数刻度自动生效
fig.add_trace(go.Box(
    x=data,  # 原始数据，无需预处理
    quartilemethod="linear",
    box_visible=True,
    points="outliers"
), row=row, col=col)
```

### 小提琴图 (Violin Plot)
**原生支持**: 完全利用Plotly 6.0原生能力，支持所有高级功能。

**技术优势** (基于Context7验证):
- KDE核密度估计在原始数据空间计算，确保统计准确性
- 支持split violins、scale groups等高级模式
- quartilemethod、scalemode等参数在对数环境下完全可用

**实现代码**:
```python
# 原生支持，无需数据预处理
fig.add_trace(go.Violin(
    x=data,  # 原始数据，保持统计计算准确性
    quartilemethod="inclusive",
    box_visible=True,
    scalemode="width",
    side="both"
), row=row, col=col)
```

### KDE密度估计 (基于Violin实现)
**实现策略**: 通过配置go.Violin实现纯KDE效果，充分利用原生对数支持。

**技术细节**:
```python
# 使用Violin实现KDE，关闭箱线图和数据点显示
fig.add_trace(go.Violin(
    x=data,  # 原始数据，KDE计算在数据空间进行
    box_visible=False,    # 隐藏内部箱线图
    points=False,         # 隐藏数据点
    meanline_visible=False, # 隐藏均值线
    scalemode='width'     # 纯密度形状
), row=row, col=col)
```

## 6.3 轴同步机制

**核心方法**: `_sync_marginal_axes`负责确保边缘图轴与主图轴的对数刻度保持一致。

**实现逻辑**:
```python
def _sync_marginal_axes(self, fig: go.Figure, has_marginal_x: bool, has_marginal_y: bool) -> None:
    """确保边缘图轴与主图对数刻度同步"""
    if has_marginal_x and self.config.xaxis.scale == ScaleType.LOG:
        # 为X边缘图的数据轴应用对数刻度
        fig.update_xaxes(type="log", row=1, col=1)

    if has_marginal_y and self.config.yaxis.scale == ScaleType.LOG:
        # 为Y边缘图的数据轴应用对数刻度
        y_row = 2 if has_marginal_x else 1
        fig.update_yaxes(type="log", row=y_row, col=2)
```

**同步范围**:
- 主图坐标轴 ↔ 对应边缘图的数据轴
- 支持单轴和双轴边缘图的不同布局场景
- 自动处理2x1、1x2、2x2等不同子图布局

## 6.4 性能与准确性优势

**零拷贝架构**:
- 原始数据直接传递给Plotly组件，无额外数据变换开销
- 避免了数据预处理可能引入的精度损失
- 充分利用Plotly底层优化的统计计算引擎

**统计准确性保证**:
- 四分位数、百分位数计算在原数据空间进行
- KDE带宽选择基于原始数据分布特征
- 异常值检测使用原始数据的统计特性

**显示一致性**:
- 边缘图与主图的对数刻度完全同步
- 刻度标签、网格线自动对齐
- 交互缩放时边缘图自动跟随

## 6.5 最佳实践建议

**数据预处理**:
1. **正值检查**: 对数刻度要求数据为正值，建议在数据层面预先过滤
2. **范围验证**: 确保数据跨度适合对数显示（避免极小的动态范围）
3. **异常值处理**: 在对数刻度下，极值的影响会被放大，建议预先处理

**配置优化**:
1. **分箱策略**: 直方图使用对数等比分箱，其他类型使用原生支持
2. **统计参数**: Box和Violin的quartilemethod建议使用"linear"以获得最佳兼容性
3. **视觉效果**: KDE的scalemode="width"在对数刻度下提供更好的视觉效果

**性能考虑**:
1. **大数据集**: 超过10万点时，KDE比直方图性能更好
2. **实时更新**: 原生支持的边缘图类型响应速度更快
3. **导出质量**: 对数刻度下的SVG导出保持矢量精度

# 7. 示例文件说明

本模块提供了20个完整的示例，展示各种功能和配置选项。以下是主要示例的说明：

## 7.1 核心功能回归测试
- **example_log_hist_regression.png**: 对数刻度与边缘直方图回归测试
- **example_log_scale.png**: 对数坐标轴与箱线图边缘图测试

## 7.2 边缘图类型展示
- **example_kde_marginal.png**: KDE（核密度估计）边缘图
- **example_violin_marginal.png**: 小提琴图边缘图
- **example_single_marginal.png**: 单轴边缘图（仅X轴）

## 7.3 数据系列与可视化
- **example_line_only.png**: 纯线条系列可视化
- **example_line_and_scatter.png**: 线条与散点组合
- **example_colorbar_with_errors.png**: 颜色轴与误差棒

## 7.4 样式与主题
- **example_style_override.png**: 自定义样式覆盖
- **example_dark_theme.png**: 深色主题演示
- **example_smart_legend_layout.png**: 智能图例布局

## 7.5 高级功能
- **example_reference_line.png**: 自定义参考线
- **example_regression_line.png**: 回归参考线
- **example_dual_colorbar.png**: 双色轴支持
- **example_advanced_tick_control.png**: 高级刻度控制
- **example_advanced_marginal_config.png**: 高级边缘图配置

## 7.6 R-12 NaN值处理演示

**example_nan_handling.png**: R-12 NaN值处理演示（显示图例）
- 展示双轨迹策略：有效数据使用Viridis色标，NaN数据显示为灰色
- NaN数据点在图例中显示为"测试数据 (NaN)"
- 配置：`nan_color="#808080"`, `nan_show_legend=True`

**example_nan_handling_no_legend.png**: R-12 NaN值处理演示（不显示图例）
- 相同的双轨迹策略，但NaN数据点不在图例中显示
- 适合需要简洁图例的场景
- 配置：`nan_color="#808080"`, `nan_show_legend=False`

## 7.7 Plotly 6.0 对数刻度原生支持演示

**example_log_scale_box.png**: Box Plots with Log Scale
- 展示Box plots在双对数刻度下的原生支持
- 配置：X轴和Y轴均为对数刻度，使用Plotly原生quartilemethod计算
- 技术特点：四分位数计算在原始数据空间进行，显示时自动应用对数变换

**example_log_scale_violin.png**: Violin Plots with Log Scale
- 展示Violin plots在双对数刻度下的完整功能支持
- 配置：包含内部箱线图、均值线、数据点显示等高级功能
- 技术特点：KDE密度估计在原数据空间计算，支持split、scalemode等高级模式

**example_log_scale_kde.png**: KDE Plots with Log Scale
- 展示基于Violin实现的纯KDE效果在对数刻度下的表现
- 配置：隐藏箱线图和数据点，仅显示平滑的密度分布
- 技术特点：核密度估计算法保持统计准确性，对数变换仅影响显示层

**技术特性总结**:
```python
# 数据自动分离
valid_data = [point for point in data if not np.isnan(point.z)]
nan_data = [point for point in data if np.isnan(point.z)]

# 有效数据轨迹：使用颜色映射
fig.add_trace(go.Scatter(..., marker=dict(color=z_vals, colorscale=cmap)))

# NaN数据轨迹：使用固定颜色
fig.add_trace(go.Scatter(..., marker=dict(color=nan_color), showlegend=show_legend))
```

所有示例文件可通过运行 `crossplot_plotly6_augment.py` 自动生成到 `crossplot_output_plotly6_augment/` 目录。

