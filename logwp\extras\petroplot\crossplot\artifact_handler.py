"""logwp.extras.petroplot.crossplot.artifact_handler - 交会图产物处理器

本模块定义了 CrossPlotArtifactHandler，一个用于处理交会图组件
所有产物（如数据快照、逻辑配置）的无状态工具类。

Architecture
------------
层次/依赖: petroplot/crossplot的内部实现层，被facade层调用
设计原则: 遵循《可追踪机器学习组件开发框架》的产物处理器模式，职责单一
"""

from __future__ import annotations

import json
from pathlib import Path
from typing import Any, Dict

import pandas as pd


class CrossPlotArtifactHandler:
    """
    一个无状态的工具类，封装了交会图组件产物的序列化和反序列化逻辑。
    """

    @staticmethod
    def save_dataframe(df: pd.DataFrame, path: Path) -> None:
        """将DataFrame保存为CSV文件。"""
        path.parent.mkdir(parents=True, exist_ok=True)
        df.to_csv(path, index=False)

    @staticmethod
    def load_dataframe(path: Path) -> pd.DataFrame:
        """从CSV文件加载DataFrame。"""
        return pd.read_csv(path)

    @staticmethod
    def save_logic_config(config_dict: Dict[str, Any], path: Path) -> None:
        """将逻辑配置字典保存为JSON文件。"""
        path.parent.mkdir(parents=True, exist_ok=True)
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)

    @staticmethod
    def load_logic_config(path: Path) -> Dict[str, Any]:
        """从JSON文件加载逻辑配置字典。"""
        with open(path, 'r', encoding='utf-8') as f:
            return json.load(f)
