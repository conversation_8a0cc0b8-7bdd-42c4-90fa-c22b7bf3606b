#!/usr/bin/env python3
"""绘图配置导出脚本。

扫描项目中所有已定义的 PlotProfile 和 PlotStyle 配置，
并将它们导出为JSON文件到 `config/plot_profiles` 目录。

Usage:
    python scripts/plotting/export_profiles_simple.py
"""

import sys
from pathlib import Path
from typing import List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def _import_profile_modules():
    """
    导入所有包含绘图配置的模块，以触发其在全局注册表中的注册。
    开发者注意：如果添加了新的包含绘t图配置的组件，请在此处添加导入。
    """
    # 导入所有包含PlotProfile/PlotStyle配置的模块，触发注册
    print("📦 加载配置模块...")
    try:
        import logwp.extras.ml.log_scout.plot_profiles
        import scape.core.validation.plot_profiles
        import scape.core.swift_pso.plot_profiles
        import scape.core.obmiq.plot_profiles
        import logwp.extras.petroplot.crossplot.plot_profiles
    except ImportError as e:
        print(f"⚠️  警告：部分配置模块加载失败，可能导致导出不完整。错误: {e}")


def _generate_manifest_content(
    plot_profiles: List[str],
    plot_styles: List[str],
    base_plot_profiles: List[str],
    base_plot_styles: List[str],
) -> str:
    """生成配置清单 (README.md) 的内容。"""

    def format_list(items: List[str], type_name: str) -> str:
        if not items:
            return "- (无)\n"
        return "".join(f"- `{item}.{type_name}.json`\n" for item in sorted(items))

    manifest = f"""# 绘图配置清单 (Plotting Configuration Manifest)

本文档由 `scripts/plotting/export_profiles.py` 自动生成，请勿手动编辑。

## 📊 配置统计

- **总配置数**: {len(plot_profiles) + len(plot_styles)}
  - `PlotProfile` (Matplotlib): {len(plot_profiles)}
  - `PlotStyle` (Plotly): {len(plot_styles)}
- **基础模板数**: {len(base_plot_profiles) + len(base_plot_styles)}
  - `PlotProfile` (Matplotlib): {len(base_plot_profiles)}
  - `PlotStyle` (Plotly): {len(base_plot_styles)}

---

## 📜 PlotProfile (Matplotlib)

### 基础模板
{format_list(base_plot_profiles, 'PlotProfile')}
### 具体模板
{format_list(list(set(plot_profiles) - set(base_plot_profiles)), 'PlotProfile')}
---

## 🎨 PlotStyle (Plotly)

### 基础模板
{format_list(base_plot_styles, 'PlotStyle')}
### 具体模板
{format_list(list(set(plot_styles) - set(base_plot_styles)), 'PlotStyle')}
---

## 🚀 使用方法

### 在代码中获取配置

```python
from logwp.extras.plotting import registry, PlotProfile, PlotStyle

# 获取一个 Matplotlib profile (自动继承合并)
profile = registry.get("validation.contribution_crossplot", expected_type=PlotProfile)

# 获取一个 Plotly style (自动继承合并)
style = registry.get("petroplot.crossplot.default", expected_type=PlotStyle)
```

### 从文件自定义配置

```bash
# 1. 复制一个现有的配置文件
cp config/plot_profiles/validation.contribution_crossplot.PlotProfile.json my_custom_profile.json

# 2. 编辑 my_custom_profile.json 文件...
```

```python
# 3. 在代码中加载并注册你的自定义配置
from logwp.extras.plotting import PlotProfile, registry

my_profile = PlotProfile.from_json("my_custom_profile.json")
registry.register(my_profile)

# 现在你可以像使用内置配置一样获取它
profile = registry.get("my_custom_profile", expected_type=PlotProfile)
```
"""
    return manifest.strip()


def _print_summary(output_dir: Path, manifest_file: Path):
    """打印最终的导出结果摘要。"""
    json_files = list(output_dir.glob("*.json"))
    print("\n✅ 导出完成!")
    print(f"📁 输出目录: {output_dir.resolve()}")
    print(f"📄 配置文件: {len(json_files)} 个")
    print(f"📖 配置清单: {manifest_file.resolve()}")

    print("\n📋 导出的配置文件:")
    for json_file in sorted(json_files):
        print(f"  - {json_file.name}")


def main():
    """脚本主函数。"""
    print("🔄 正在导出绘图配置...")

    try:
        _import_profile_modules()

        from logwp.extras.plotting import PlotProfile, PlotStyle, registry

        output_dir = Path("config/plot_profiles")
        output_dir.mkdir(parents=True, exist_ok=True)

        all_plot_profiles = registry.list_profiles(expected_type=PlotProfile)
        all_plot_styles = registry.list_profiles(expected_type=PlotStyle)
        base_plot_profiles = registry.list_base_profiles(expected_type=PlotProfile)
        base_plot_styles = registry.list_base_profiles(expected_type=PlotStyle)

        print(f"\n📊 发现 {len(all_plot_profiles)} 个 PlotProfile 和 {len(all_plot_styles)} 个 PlotStyle。")

        print(f"💾 正在导出所有配置到: {output_dir}")
        registry.save_to_dir(output_dir)

        manifest_content = _generate_manifest_content(
            all_plot_profiles, all_plot_styles, base_plot_profiles, base_plot_styles
        )
        manifest_file = output_dir / "README.md"
        manifest_file.write_text(manifest_content, encoding="utf-8")
        _print_summary(output_dir, manifest_file)

    except Exception as e:
        print(f"\n❌ 导出失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
