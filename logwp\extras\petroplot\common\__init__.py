"""logwp.extras.petroplot.common - 通用绘图组件基础

本包是 `petroplot` 绘图框架的核心，提供了所有绘图组件可复用的基础模块。

它遵循“关注点分离”和“组合优于继承”原则，将通用逻辑划分为：
- **抽象基类 (`plotter_abc`)**: 定义所有绘图器的通用接口和模板方法。
- **配置模型 (`config`)**: 定义与具体图表无关的、可复用的视觉映射配置。
- **布局管理器 (`layout_manager`, `legend_manager`)**: 提供模块化的、可组合的布局辅助工具。
- **门面辅助函数 (`facade_utils`)**: 提供在门面层可复用的通用工具。
"""

from .config import (
    AxisConfig,
    BaseLine,
    CartesianPlotConfig,
    ColorBarConfig,
    CategoricalColorConfig,
    ContinuousColorConfig,
    DataMarkerStyle,
    DiagonalLineConfig,
    LegendConfig,
    LineConfig,
    LineStyle,
    MarginalPlotConfig,
    NullMarkerStyle,
    SymbolConfig,
    TickConfig,
)
from .exceptions import PetroPlotError
from .facade_utils import save_and_register_plots, save_and_register_plotly_plots
from .layout_manager import LayoutManager
from .legend_manager import LegendManager
from .plotter_abc import CartesianPlotter, PetroPlotter, TernaryPlotter

__all__ = [
    # from config
    "AxisConfig",
    "BaseLine",
    "CartesianPlotConfig",
    "CategoricalColorConfig",
    "ColorBarConfig",
    "ContinuousColorConfig",
    "DataMarkerStyle",
    "DiagonalLineConfig",
    "LegendConfig",
    "LineConfig",
    "LineStyle",
    "MarginalPlotConfig",
    "NullMarkerStyle",
    "SymbolConfig",
    "TickConfig",
    # from exceptions
    "PetroPlotError",
    # from plotter_abc
    "PetroPlotter",
    "CartesianPlotter",
    "TernaryPlotter",
    # from layout_manager
    "LayoutManager",
    # from legend_manager
    "LegendManager",
    # from facade_utils
    "save_and_register_plots",
    "save_and_register_plotly_plots",
]
