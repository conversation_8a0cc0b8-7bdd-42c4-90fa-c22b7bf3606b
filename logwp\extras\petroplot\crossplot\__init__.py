"""logwp.extras.petroplot.crossplot - 交会图组件

本组件提供了一个功能强大、可配置的交会图生成工具，支持：
- 多数据系列绘制
- 边缘分布图 (直方图, 箱线图, 小提琴图, KDE)
- 对数坐标轴和线性坐标轴
- 灵活的样式配置和可复现性

核心API:
- run_crossplot_step: 用于工作流的标准步骤门面。
- CrossPlotConfig: 用于定义绘图逻辑和表现的Pydantic模型。
- replot_crossplot_from_snapshot: 从快照精确复现图表。
"""

from __future__ import annotations

# 1. 导入公共API
from .config import CrossPlotConfig, SeriesConfig
from .constants import CrossPlotProfiles, CrossPlotArtifacts
from .facade import run_crossplot_step, replot_crossplot_from_snapshot
from .artifact_handler import CrossPlotArtifactHandler
from .presets import create_publication_single_series_scatter_config

# 2. 导入此模块以在启动时注册绘图模板
from . import plot_profiles

# 3. 定义 __all__
__all__ = [
    # --- Functions ---
    "run_crossplot_step",
    "replot_crossplot_from_snapshot",
    "create_publication_single_series_scatter_config",

    # --- Config Models ---
    "CrossPlotConfig",
    "SeriesConfig",

    # --- Constants ---
    "CrossPlotProfiles",
    "CrossPlotArtifacts",

    # --- Handlers ---
    "CrossPlotArtifactHandler",
]
