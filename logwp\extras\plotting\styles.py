"""logwp.extras.plotting.styles - Plotly绘图样式模型

定义与具体绘图库无关的、可序列化的样式配置模型。
支持类似PlotProfile的层叠继承和合并。

Architecture
------------
层次/依赖: logwp.extras.plotting包核心层
设计原则: 数据驱动配置、Pydantic模型、类型安全
"""

from __future__ import annotations
from copy import deepcopy

from pathlib import Path
from typing import Any, Dict, Optional, TYPE_CHECKING, Union

from pydantic import BaseModel, Field, ConfigDict

from .exceptions import ProfileNotFoundError

if TYPE_CHECKING:
    from .registry import PlottingConfigRegistry

def _deep_merge_dicts(base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
    """
    深度合并两个字典。override中的值会覆盖base中的值。
    如果两个字典在同一个键上都有字典值，则递归合并。
    """
    result = deepcopy(base)
    for key, value in override.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = _deep_merge_dicts(result[key], value)
        else:
            result[key] = deepcopy(value)
    return result


class PlotStyle(BaseModel):
    """与具体实现库无关的绘图样式模板。

    使用Pydantic模型定义，可轻松序列化为JSON。
    支持与PlotProfile相同的两级层叠继承体系。

    通过 `extra='allow'` 支持扩展属性，使其可以作为灵活的样式组容器。

    Attributes:
        name (str): 样式的唯一名称
        config_type (str): 类型标识符，用于反序列化
        font (Dict[str, Any]): 字体相关配置
        color (Dict[str, Any]): 颜色相关配置
        linewidth (Dict[str, Any]): 线宽相关配置
        tick (Dict[str, Any]): 刻度线相关配置
        frame (Dict[str, Any]): 图表边框相关配置
    """
    model_config = ConfigDict(extra="allow")
    name: str = "unnamed_style"
    config_type: str = Field("PlotStyle", frozen=True)
    font: Dict[str, Any] = Field(default_factory=dict)
    color: Dict[str, Any] = Field(default_factory=dict)
    linewidth: Dict[str, Any] = Field(default_factory=dict)
    tick: Dict[str, Any] = Field(default_factory=dict)
    frame: Dict[str, Any] = Field(default_factory=dict)

    def _extract_module_base_name(self, profile_name: str) -> Optional[str]:
        """从配置名称提取模块级基础模板名称。"""
        if "." not in profile_name:
            return None
        module_prefix = profile_name.split(".")[0]
        return f"{module_prefix}.base"

    def merge_with_base(self, base_style: "PlotStyle") -> "PlotStyle":
        """与基础样式合并，创建新的配置实例。

        此方法现在是动态的，可以处理在类定义之外添加的扩展属性
        （例如 'marker', 'backend_specific'）。它会合并所有基于字典的样式组。
        """
        # 使用 model_dump 获取所有数据，包括 extras
        base_data = base_style.model_dump()
        override_data = self.model_dump()

        # 从基础数据开始，然后用覆盖数据进行深度合并
        merged_data = _deep_merge_dicts(base_data, override_data)

        # 确保名称来自覆盖样式（self）
        merged_data['name'] = self.name

        # 使用 model_validate 从字典创建实例，这能正确处理 extras
        return PlotStyle.model_validate(merged_data)

    def resolve(self, registry: "PlottingConfigRegistry") -> PlotStyle:
        """解析配置，执行与PlotProfile行为一致的两级层叠合并。

        合并顺序: `当前模板` -> `模块级基础模板` -> `全局基础模板`

        Args:
            registry: 用于查找基础模板的配置注册表实例。

        Returns:
            一个经过完整层叠合并的新的PlotStyle实例。
        """
        # Start with the current instance
        result = self

        # 1. Merge with module-level base
        module_base_name = self._extract_module_base_name(self.name)
        if module_base_name:
            try:
                module_base = registry.get_base(module_base_name, expected_type=PlotStyle, clone=False)
                result = result.merge_with_base(module_base)
            except ProfileNotFoundError:
                pass  # Module-level base is optional

        # 2. Merge with global base
        try:
            global_base = registry.get_base("base", expected_type=PlotStyle, clone=False)
            result = result.merge_with_base(global_base)
        except (ProfileNotFoundError, TypeError):
            # Global 'base' for PlotStyle is optional.
            # A TypeError occurs if the global 'base' is a PlotProfile, which is fine.
            pass

        return result

    def to_json(self, filepath: Union[str, Path]) -> None:
        """将样式配置保存到JSON文件。

        Args:
            filepath: 保存路径
        """
        Path(filepath).write_text(self.model_dump_json(indent=2), encoding='utf-8')


# --- 默认全局基础模板 ---
from . import theme_constants as themes

_default_global_base_style = PlotStyle(
    name="base",
    font={
        "family": themes.DEFAULT_FONT_FAMILY,
        "color": themes.TEXT_COLOR_LIGHT,
        "size_title": themes.FONT_SIZE_TITLE,
        "size_label": themes.FONT_SIZE_LABEL,
        "size_ticks": themes.FONT_SIZE_TICKS,
    },
    color={
        "cycle": themes.PLOTLY_COLOR_CYCLE,
        "background_plot": themes.BACKGROUND_PLOT_LIGHT,
        "background_canvas": themes.BACKGROUND_CANVAS_LIGHT,
        "grid_major": themes.GRID_MAJOR_LIGHT,
        "grid_minor": themes.GRID_MINOR_LIGHT,
        "tick_major": themes.FRAME_COLOR_LIGHT,
        "tick_minor": themes.GRID_MAJOR_LIGHT,
    },
    linewidth={
        "grid_major": themes.LINEWIDTH_GRID_MAJOR,
        "grid_minor": themes.LINEWIDTH_GRID_MINOR,
        "tick_major": themes.LINEWIDTH_TICK_MAJOR,
        "tick_minor": themes.LINEWIDTH_TICK_MINOR,
        "reference_line": themes.LINEWIDTH_REFERENCE,
    },
    tick={"major_length": 6, "minor_length": 4},
    frame={"color": themes.FRAME_COLOR_LIGHT, "width": themes.LINEWIDTH_FRAME},
)
