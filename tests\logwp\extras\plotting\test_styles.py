"""测试通用样式模型 (styles.py)。

测试PlotStyle的功能，包括：
- Pydantic模型创建和验证
- JSON序列化和反序列化
- 层叠合并逻辑
"""

import json
import pytest
from pathlib import Path
from unittest.mock import MagicMock

from logwp.extras.plotting import PlotStyle, PlottingConfigRegistry
from logwp.extras.plotting.exceptions import ProfileNotFoundError


@pytest.fixture
def base_plot_style() -> PlotStyle:
    """提供一个基础PlotStyle模板。"""
    return PlotStyle(
        name="base",
        font={"family": "Arial", "size_label": 12},
        color={"background_plot": "#ffffff", "cycle": ["#000000"]},
    )


@pytest.fixture
def module_base_plot_style() -> PlotStyle:
    """提供一个模块级基础PlotStyle模板。"""
    return PlotStyle(
        name="test.base",
        font={"family": "Times New Roman"}, # 覆盖全局
        color={"background_plot": "#eeeeee"}, # 覆盖全局
    )


@pytest.fixture
def specific_plot_style() -> PlotStyle:
    """提供一个具体的PlotStyle模板。"""
    return PlotStyle(
        name="test.specific",
        font={"size_label": 14}, # 覆盖基础
        frame={"width": 2.0}, # 新增
    )


class TestPlotStyle:
    """测试PlotStyle模型。"""

    def test_creation(self, sample_plot_style):
        """测试PlotStyle的创建和默认值。"""
        style = sample_plot_style
        assert style.name == "test.sample"
        assert style.config_type == "PlotStyle"
        assert style.font["family"] == "Verdana"
        assert style.color["background_plot"] == "#f0f0f0"

    def test_json_serialization(self, sample_plot_style, temp_dir):
        """测试PlotStyle的JSON序列化。"""
        style = sample_plot_style
        json_path = temp_dir / "style.json"
        style.to_json(json_path)

        assert json_path.exists()
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        assert data["name"] == "test.sample"
        assert data["config_type"] == "PlotStyle"
        assert data["font"]["family"] == "Verdana"
        assert data["color"]["cycle"] == ["#ff0000", "#00ff00", "#0000ff"]

    def test_json_deserialization(self, sample_plot_style, temp_dir):
        """测试PlotStyle的JSON反序列化。"""
        style = sample_plot_style
        json_path = temp_dir / "style.json"
        style.to_json(json_path)

        with open(json_path, 'r', encoding='utf-8') as f:
            json_string = f.read()

        loaded_style = PlotStyle.model_validate_json(json_string)
        assert loaded_style == style

    def test_merge_with_base(self, base_plot_style, specific_plot_style):
        """测试PlotStyle的深度合并逻辑。"""
        merged = specific_plot_style.merge_with_base(base_plot_style)

        # 验证合并结果
        assert merged.name == "test.specific" # 保持目标名称
        # font: specific覆盖size, base提供family
        assert merged.font["size_label"] == 14
        assert merged.font["family"] == "Arial"
        # color: base提供所有
        assert merged.color["background_plot"] == "#ffffff"
        # frame: specific提供所有
        assert merged.frame["width"] == 2.0

    def test_resolve_inheritance(
        self,
        base_plot_style,
        module_base_plot_style,
        specific_plot_style
    ):
        """测试完整的两级层叠合并解析。"""
        # 创建一个模拟的注册表
        mock_registry = MagicMock(spec=PlottingConfigRegistry)

        # 配置模拟注册表的get_base方法
        def mock_get_base(name, expected_type, clone):
            if name == "base":
                return base_plot_style
            if name == "test.base":
                return module_base_plot_style
            raise ProfileNotFoundError(f"Base profile '{name}' not found")

        mock_registry.get_base.side_effect = mock_get_base

        # 执行解析
        resolved = specific_plot_style.resolve(mock_registry)

        # 验证合并顺序: specific -> module_base -> base
        # font: specific覆盖size, module_base覆盖family
        assert resolved.font["size_label"] == 14
        assert resolved.font["family"] == "Times New Roman"
        # color: module_base覆盖background_plot, base提供cycle
        assert resolved.color["background_plot"] == "#eeeeee"
        assert resolved.color["cycle"] == ["#000000"]
        # frame: specific提供
        assert resolved.frame["width"] == 2.0
