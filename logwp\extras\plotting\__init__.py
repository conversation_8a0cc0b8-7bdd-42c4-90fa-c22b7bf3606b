"""logwp.extras.plotting - 现代化绘图配置服务

提供基于配置模板的绘图系统，支持两级继承体系和用户自定义配置。

Architecture
------------
层次/依赖: logwp包扩展层，依赖models、utils、constants
设计原则: 配置即服务、模板化管理、关注点分离
性能特征: 延迟导入、配置缓存、JSON序列化

Core Features
-------------
- **PlotProfile**: 绘图配置模板，支持继承和合并
- **PlottingConfigRegistry**: 中心化配置注册表，支持两级基础模板
- **配置即服务**: 从"参数传递"升级到"配置驱动"模式
- **用户友好**: 支持JSON外部配置文件，无需修改代码

Package Structure
-----------------
- profiles.py: 核心数据模型 (PlotProfile, SaveConfig)
- registry.py: 配置注册表和继承体系
- styler.py: 样式应用工具函数
- saver.py: 图像保存工具函数
- exceptions.py: 专属异常类

Examples
--------
>>> from logwp.extras.plotting import registry, PlotProfile, apply_profile
>>>
>>> # 获取预定义配置
>>> profile = registry.get("plt_analyzer.capture_curve")
>>>
>>> # 应用到matplotlib
>>> fig, ax = plt.subplots()
>>> apply_profile(ax, profile)
>>> ax.plot(x, y, **profile.artist_props)

References
----------
- 《绘图系统全新重构设计文档》- 完整架构设计
- 《SCAPE_CCG_编码与通用规范》- 编码规范要求
"""

# 直接导入避免延迟导入问题
from .registry import registry, PlottingConfigRegistry
from .profiles import PlotProfile, SaveConfig
from .styles import PlotStyle
from .styler import apply_profile
from .saver import save_figure
from .exceptions import (
    WpPlottingError,
    ProfileNotFoundError,
    ProfileRegistrationError,
    ProfileMergeError,
    ProfileIOError,
    StyleApplicationError,
)


__all__ = [
    "registry",
    "PlottingConfigRegistry",
    "PlotProfile",
    "PlotStyle",
    "SaveConfig",
    "apply_profile",
    "save_figure",
    # Exceptions
    "WpPlottingError",
    "ProfileNotFoundError",
    "ProfileRegistrationError",
    "ProfileMergeError",
    "ProfileIOError",
    "StyleApplicationError",
]
