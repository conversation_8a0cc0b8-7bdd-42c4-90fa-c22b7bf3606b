"""logwp.extras.plotting.theme_constants - 全局主题常量

定义项目级共享的视觉识别常量，作为所有绘图配置的“单一事实来源”。
该文件确保了无论是基于 Matplotlib 的 PlotProfile 还是基于 Plotly 的 PlotStyle，
都能在核心视觉元素上保持一致性。

Architecture
------------
层次/依赖: logwp.extras.plotting包核心层，被plotting.profiles和plotting.styles使用
设计原则: 单一事实来源 (SSOT), DRY (Don't Repeat Yourself)
"""

from __future__ import annotations

# ==============================================================================
# 颜色常量 (Color Constants)
# ==============================================================================

# --- 主品牌色 ---
# 基于 Plotly 的默认色板，具有良好的视觉区分度
PRIMARY_BRAND_COLOR = "#1f77b4"  # Muted Blue
SECONDARY_BRAND_COLOR = "#ff7f0e" # Safety Orange
SUCCESS_COLOR = "#2ca02c"       # Cooked Asparagus Green
DANGER_COLOR = "#d62728"        # Brick Red
INFO_COLOR = "#9467bd"          # Muted Purple

# --- 完整色板 ---
# Plotly v4 'plotly' template color sequence
# https://plotly.com/python/discrete-color/
PLOTLY_COLOR_CYCLE = [
    '#1f77b4',  # muted blue
    '#ff7f0e',  # safety orange
    '#2ca02c',  # cooked asparagus green
    '#d62728',  # brick red
    '#9467bd',  # muted purple
    '#8c564b',  # chestnut brown
    '#e377c2',  # raspberry yogurt pink
    '#7f7f7f',  # middle gray
    '#bcbd22',  # curry yellow-green
    '#17becf'   # blue-teal
]

# --- 灰度色 ---
BACKGROUND_CANVAS_LIGHT = "#ffffff"
BACKGROUND_PLOT_LIGHT = "#ffffff"
BACKGROUND_CANVAS_DARK = "#2f2f2f"
BACKGROUND_PLOT_DARK = "#3f3f3f"

GRID_MAJOR_LIGHT = "#bbbbbb"
GRID_MINOR_LIGHT = "#dddddd"
GRID_MAJOR_DARK = "#666666"
GRID_MINOR_DARK = "#555555"

FRAME_COLOR_LIGHT = "#666666"
FRAME_COLOR_DARK = "#cccccc"

TEXT_COLOR_LIGHT = "#333333"
TEXT_COLOR_DARK = "#ffffff"


# ==============================================================================
# 字体常量 (Font Constants)
# ==============================================================================

DEFAULT_FONT_FAMILY = "Arial"
PUBLICATION_FONT_FAMILY = "Times New Roman"
MONOSPACE_FONT_FAMILY = "Courier New"

# --- 字号 (pt) ---
FONT_SIZE_TITLE = 14.0
FONT_SIZE_LABEL = 12.0
FONT_SIZE_TICKS = 10.0
FONT_SIZE_LEGEND = 10.0
FONT_SIZE_ANNOTATION = 9.0


# ==============================================================================
# 线宽常量 (Linewidth Constants)
# ==============================================================================

LINEWIDTH_MAIN = 1.2
LINEWIDTH_REFERENCE = 1.0
LINEWIDTH_GRID_MAJOR = 0.6
LINEWIDTH_GRID_MINOR = 0.4
LINEWIDTH_FRAME = 0.8
LINEWIDTH_TICK_MAJOR = 1.0
LINEWIDTH_TICK_MINOR = 0.8
