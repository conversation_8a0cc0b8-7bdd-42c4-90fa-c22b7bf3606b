# `logwp.extras.petroplot.crossplot` - 交互式交会图组件

**版本**: 1.0
**状态**: 生产就绪

### 摘要 (Abstract)

本组件 (`crossplot`) 的核心设计哲学是**“强强联合，智能包装”**。我们旨在充分利用 **Plotly 6.0+** 这一现代化、交互式绘图库本身出色的**原生交会图功能**（如边缘图、灵活的颜色轴、丰富的悬停信息），通过一层轻量而智能的适配，将其无缝集成到 SCAPE 的可追踪机器学习框架中。

最终目标是为测井、油气勘探及储层评价领域，提供一个功能强大、高度可配置、完全可复现的交会图分析**步骤包 (Step Package)**。

**开发前需明确的关键决策 (Pre-Development Decisions):**

1.  **技术选型 (Technology Selection):**
    *   **绘图后端**: 明确选择 **Plotly** 作为后端。因为它不仅能生成出版物级别的静态图表，其强大的**交互性**（缩放、平移、悬停、选择）更是数据探索阶段不可或缺的利器。
    *   **样式模型**: 明确选择 **`PlotStyle`** 而非 `PlotProfile`。`PlotProfile` 是为 Matplotlib 的 `rcParams` 体系量身定制的。而 `PlotStyle` 是一个更通用、与后端无关的样式模型，它通过结构化的字典（如 `font`, `color`）来定义美学元素，完美契合 Plotly 等现代绘图库的配置方式。**原则：为正确的工具选择正确的配置模型。**

2.  **API分层 (API Layering):** 必须设计满足不同场景的API。本组件提供了 `run_crossplot_step` (用于工作流) 和 `replot_crossplot_from_snapshot` (用于复现)。

3.  **配置驱动 (Configuration-Driven):** 必须将**绘图逻辑**（在 `CrossPlotConfig` 中定义，如数据源、坐标轴范围、是否显示边缘图）与**美学样式**（在 `PlotStyle` 中定义，如颜色、字体、线宽）彻底分离。

4.  **可复现性设计 (Reproducibility by Design):** 必须将**数据快照**和**逻辑快照**作为与图表同等重要的一等公民产物，以实现完全可复现。

**编程时需遵循的核心实践 (Core Implementation Practices):**

*   **“翻译官”模式 (Translator Pattern):** 必须解耦用户意图（逻辑曲线名）与内部实现（物理列名），`facade.py` 中的 `_resolve_selectors` 函数是此模式的典范。
*   **嵌入领域知识 (Embed Domain Knowledge):** 应通过**便捷预设函数**（如 `create_publication_single_series_scatter_config`）将行业标准（如出版物图表尺寸、科学色阶）固化为易于调用的配置。
*   **提升用户透明度 (Enhance User Transparency):** 任何可能影响结果的“静默”数据修正（如对数转换时的值裁剪），都**必须通过日志向用户发出明确警告**。

**核心架构原则 (Core Architectural Principles):**

*   **智能封装 (Intelligent Wrapping):** 组件的核心是作为Plotly原生功能的智能“包装器”和“装配工”，而非重新发明轮子。
*   **分层解耦 (Layered Decoupling):** 严格遵循`facade` (门面), `config` (逻辑配置), `plotter` (渲染引擎), `presets` (便捷API) 的四层架构，职责清晰。
*   **配置驱动与样式分离 (Config-Driven & Style Separation):** 通过`CrossPlotConfig` (定义“画什么”) 和必需的`PlotStyle` (定义“怎么画”) 两个独立对象，强制实现逻辑与美学的分离。
*   **双API设计 (Dual API Design):** 同时提供`presets`中的便捷函数（用于80%的常规场景）和直接操作`CrossPlotConfig`的底层API（用于20%的复杂场景），兼顾易用性与灵活性。
*   **原生能力暴露 (Native Power Exposure):** `config.py`中的Pydantic模型被设计为Plotly原生功能的类型安全、结构化映射，确保用户可以访问其全部能力。
*   **可扩展设计 (Extensible by Design):** 广泛采用策略模式（如`MARGINAL_DRAWERS`）和自配置模型（如`MarginalConfig.get_current_style()`），使得新增功能（如新的边缘图类型）无需修改核心代码。
*   **可复现性设计 (Reproducibility by Design):** 将数据快照和逻辑快照作为与图表同等重要的一等公民产物，确保任何图表都可以被精确复现。

---
## 1. 架构与设计

本组件的设计旨在以最小的复杂度，最大化地释放 Plotly 的原生能力，并将其融入到 SCAPE 的工程化体系中。

### 1.1. 架构与设计亮点 (Architectural Highlights)

1.  **Plotly 原生能力的智能封装**:
    *   我们没有重新发明轮子，而是将 Plotly 强大的 `go.Scatter` 轨迹和 `layout` 对象作为核心。
    *   `internal/plotter.py` 中的 `CrossPlotter` 类扮演着“智能装配工”的角色。它接收高级、语义化的 `CrossPlotConfig` 对象，并将其精确地翻译成 Plotly 所需的底层字典结构。
    *   **示例**: 用户在 `CrossPlotConfig` 中只需设置 `marginal.enabled = MarginalEnabled.BOTH`，`CrossPlotter` 就会自动为 Plotly 的 `go.Scatter` 配置正确的 `xaxis`, `yaxis`, `marginal_x`, `marginal_y` 属性。

2.  **`PlotStyle`：为现代绘图而生的样式系统**:
    *   `crossplot` 组件坚定地选择了 `PlotStyle` 作为其样式配置模型。这是因为 `PlotStyle` 的设计初衷就是为了服务于像 Plotly 这样通过**结构化字典**来配置样式的现代库。
    *   与 `PlotProfile` 紧密耦合 Matplotlib 的 `rcParams` 不同，`PlotStyle` 将样式分为 `font`, `color`, `frame` 等逻辑分组，这与 Plotly `layout` 对象的结构天然契合。
    *   `CrossPlotter` 会消费一个经过层叠合并的 `PlotStyle` 对象，并将其中的值（如 `style.font.size_label`）应用到 Plotly `layout` 的相应位置（如 `layout.xaxis.title.font.size`），实现了样式与逻辑的完美分离。

3.  **完美的分层与解耦**:
    *   **`facade.py` (门面层)**: 提供 `run_crossplot_step` 作为与工作流集成的唯一公共入口。它负责编排整个流程：解析用户意图、调用绘图引擎、保存并注册产物。
    *   **`config.py` (配置层)**: 使用 Pydantic 定义了所有**绘图逻辑**。用户通过 `CrossPlotConfig` 声明“画什么”和“怎么画”，例如使用哪些数据、坐标轴是否对数化、是否显示参考线等。
    *   **`internal/plotter.py` (引擎层)**: 这是一个纯粹的“渲染器”。它接收解析好的配置和数据，并负责生成 Plotly 图表对象，不包含任何业务逻辑。
    *   **`presets.py` (便捷层)**: 提供 `create_publication_single_series_scatter_config` 这样的“工厂函数”，将复杂的配置过程封装成一个简单的调用，极大地提升了用户体验。

4.  **卓越的可复现性与可追踪性**:
    *   **数据快照**: `run_crossplot_step` 在生成图表的同时，会自动保存一份用于绘图的所有数据源的 CSV 快照。
    *   **逻辑快照**: 同时，它还会保存一份 `logic_config.json`，其中包含了绘图所用的全部 `CrossPlotConfig` 配置和解析后的列名。
    *   **产物追踪**: 所有产物（图表、数据快照、逻辑快照）都被 `ctx.register_artifact` 注册，并使用 `constants.py` 中定义的规范化名称，完全符合可追踪机器学习组件的标准。

### 1.2. 核心特性

- **多系列支持**: 可在同一张图上绘制多个数据系列，并独立配置其样式。
- **边缘图**: 原生支持边缘直方图、箱线图、小提琴图和KDE图。
- **灵活的坐标轴**: 支持线性和对数刻度，并可精细控制范围、刻度和网格线。
- **强大的颜色轴**: 支持连续和离散颜色映射，支持对数变换，并可自定义颜色条的样式和位置。
- **可复现性**: 支持从数据和逻辑快照精确复现图表，将耗时的上游计算与轻量的绘图调试解耦。
- **可定制化**: 通过 `logwp.extras.plotting` 的 `PlotStyle` 系统轻松定制图表外观，无需修改代码。

### 1.3. 高级功能与实现细节 (Advanced Features & Implementation Details)

本组件不仅提供了强大的基础功能，还在内部实现了一系列针对测井和勘探领域的特色功能，体现了“智能包装”的设计哲学。

1.  **自动绘图拆分 (`split_by_curve`)**:
    *   **功能**: 用户只需在 `SeriesConfig` 中指定一个 `split_by_curve` 的列名（如 `ZONE`），组件便会自动根据该列的唯一值，为每个值生成一张独立的、样式一致的交会图。
    *   **实现**: 此逻辑位于 `facade.py` 的 `run_crossplot_step` 函数中。它在绘图前对数据进行分组，并为每个数据子集调用一次绘图引擎，同时自动更新图表标题以反映当前的拆分值（如 "Cross-Plot (ZONE-A)"）。这极大地简化了按类别进行对比分析的流程。

2.  **颜色轴对数变换 (`log_transform`)**:
    *   **功能**: 对于跨越多个数量级的数据（如渗透率），用户只需在 `ColorbarConfig` 中设置 `log_transform=True`，颜色轴便会基于数据的对数值进行映射。
    *   **实现**: 该功能在数据预处理阶段（`facade.py` 的 `_resolve_selectors`）完成。它会自动：
        1.  创建一个新的临时列（如 `PERM_log10`）。
        2.  在计算对数前，**自动将所有非正值（<=0）截断为一个极小的正数**，并**通过日志向用户发出明确警告**，确保了计算的健壮性和结果的透明度。
        3.  下游的绘图引擎只需消费这个新的对数化列，无需关心变换的细节。

3.  **对数刻度下的直方图智能优化**:
    *   **问题**: 在对数坐标轴上绘制标准的边缘直方图，会导致视觉失真。因为直方图的箱（bin）在**线性空间**是等宽的，但在**对数空间**中会变得宽度不一，无法准确反映数据的对数分布。
    *   **解决方案**: `internal/plotter.py` 中的 `_calculate_logarithmic_bins` 方法专门处理此问题。当它检测到用户同时请求了**对数刻度**和**边缘直方图**时，它会：
        1.  放弃使用 Plotly 的自动分箱。
        2.  在**对数空间**中计算等宽的箱边界。
        3.  使用 `np.logspace` 将这些边界转换回**线性空间**。
        4.  将这些非等宽的、但在对数尺度下视觉均匀的箱边界传递给 Plotly。
    *   **效果**: 最终生成的边缘直方图，其条带宽度在视觉上与主图的对数刻度完美匹配，准确地反映了数据的对数分布特征。这是将领域知识深度嵌入组件内部的典型范例。

4.  **可扩展的边缘图实现 (Extensible Marginal Plot Implementation)**:
    *   **设计模式**: 我们采用了**策略模式 (Strategy Pattern)** 来实现边缘图的绘制。`internal/marginal_plot_helper.py` 中的 `MARGINAL_DRAWERS` 字典是此模式的核心，它将边缘图的类型（如 `HIST`, `BOX`）映射到具体的绘制器类。
    *   **可扩展性**: 这种设计使得添加新的边缘图类型变得异常简单。开发者只需：
        1.  创建一个新的、实现了标准绘制接口的“绘制器”类。
        2.  将这个新的绘制器类注册到 `MARGINAL_DRAWERS` 字典中。
    *   **优势**: 无需修改核心的 `CrossPlotter` 逻辑，组件的功能就能得到扩展。这保证了系统的开放/封闭原则，提高了代码的可维护性和可扩展性。

### 1.4. 更深层次的架构洞察与最佳实践 (Deeper Architectural Insights & Best Practices)

除了上述功能，组件的内部实现还体现了更多值得借鉴的架构决策。

1.  **双配置模型解耦 (Decoupling via Dual Configuration Models)**:
    *   **模式**: 组件巧妙地使用了两种Pydantic模型来解耦用户意图与引擎实现。`config.py` 中定义的 `CrossPlotConfig` 及其子模型是**面向用户的API**，它使用**逻辑曲线名**。而 `_resolve_selectors` 函数则将其“翻译”为内部使用的 `CrossPlotColumnSelectors` 模型，该模型包含的是**物理列名**。
    *   **架构优势**: 这种模式将绘图引擎 `CrossPlotter` 与用户的数据命名约定完全解耦。引擎只关心已解析的、确切的物理列名，这使得它更加健壮、可测试，并且可以轻松地适配来自不同数据源（只要它们能被解析）的数据。

2.  **便捷预设作为用户友好API层 (Convenience Presets as a User-Friendly API Layer)**:
    *   **问题**: 核心的 `CrossPlotConfig` 模型因其嵌套结构而具备极高的灵活性和功能性，但这也导致了在常见场景下手动构建它会变得冗长和复杂。
    *   **解决方案**: 我们在 `presets.py` 中引入了 `create_publication_single_series_scatter_config` 这样的“工厂函数”。这些函数作为一层高级的、用户友好的API。
    *   **工作机制**: 它们接收一组扁平化的、符合直觉的参数（如 `x_title`, `y_log`），然后在内部处理实例化和组装嵌套的 `AxisConfig`, `ColorbarConfig` 等对象的复杂工作。同时，它们还将出版物级别的最佳实践（如网格颜色、图框样式）作为默认值嵌入其中。
    *   **架构优势**: 这种设计将**高级用户API**（直接操作 `CrossPlotConfig`）与**日常用户API**（调用简单的预设函数）分离开来。它在保留底层配置系统全部能力的同时，极大地降低了使用该组件的门槛，是“嵌入领域知识”原则的关键体现。

3.  **强制的逻辑与样式分离 (Enforced Separation of Logic and Style)**:
    *   **模式**: 主配置模型 `CrossPlotConfig` 中有一个**必需**字段 `style: PlotStyle`。这并非一个可选项，而是API契约的一部分。
    *   **工作机制**: 这种设计在编程层面强制开发者必须提供两个独立的对象：一个用于定义图表的**逻辑**（`CrossPlotConfig`自身），另一个用于定义其**美学**（`PlotStyle`对象）。
    *   **架构优势**: 它从根本上杜绝了将样式参数（如`marker_color`）混入逻辑配置的可能，使得配置系统更加纯粹和可维护。这也使得为同一份绘图逻辑应用一套完全不同的“主题”（即传入一个不同的`PlotStyle`对象）变得轻而易举，是“配置驱动”原则的健壮实现。

4.  **自配置的、状态感知的配置模型 (Self-Configuring, State-Aware Configuration Models)**:
    *   **模式**: 我们在 `config.py` 中利用了 Pydantic 模型的高级特性，实现了“状态感知”的配置。以 `MarginalConfig` 为例，它内部包含了 `hist_style`, `box_style`, `violin_style` 等所有可能的样式子模型。
    *   **工作机制**: `MarginalConfig` 提供了一个 `get_current_style()` 方法，该方法会根据 `kind` 字段的当前值（状态），动态地返回对应的样式子模型。
    *   **架构优势**: 这种设计将复杂的、依赖于状态的配置逻辑封装在了配置模型自身内部。绘图引擎 `CrossPlotter` 无需编写 `if/elif/else` 来判断使用哪个样式，它只需调用 `config.marginal.get_current_style()` 即可获得正确的配置对象。这使得绘图引擎的逻辑更简洁，同时让配置对象本身变得更加智能和自洽。

5.  **标准化的可追踪产物处理器 (Standardized Traceable Artifact Handler)**:
    *   **模式**: 组件遵循了《可追踪机器学习组件开发框架》规范，实现了一个专用的、无状态的 `artifact_handler.py`。
    *   **工作机制**: 这个处理器封装了所有与“可复现性”相关的I/O操作，包括 `save_dataframe`, `load_dataframe`, `save_logic_config`, `load_logic_config`。`facade.py` 中的主流程函数 `run_crossplot_step` 委托 `ArtifactHandler` 来处理所有产物的序列化和反序列化。
    *   **架构优势**:
        *   **职责单一**: 将产物处理的底层细节（如使用 `pandas.to_csv` 还是 `pickle`，JSON的格式化方式等）与主业务流程（`facade.py`）完全解耦。
        *   **一致性与可维护性**: 确保了所有产物的保存和加载方式在整个组件中是统一的。如果未来需要改变存储格式（例如从CSV切换到Parquet），只需修改 `ArtifactHandler` 这一个地方。
        *   **模板化**: `ArtifactHandler` 的设计本身就是一个可复用的模式，可以被其他组件借鉴，以快速实现标准化的产物管理。

6.  **智能布局管理 (Intelligent Layout Management)**:
    *   **自适应图例**: `internal/plotter.py` 中的 `_get_legend_config` 方法会**自动调整图例的位置**。当检测到右侧有颜色轴时，它会自动将图例移动到图表内部的左上角，以避免重叠，从而提升了默认布局的美观性和可用性。
    *   **上下文感知坐标轴样式**: `_apply_global_axis_styling` 方法为边缘图的坐标轴应用了与主图不同的样式。例如，它会自动隐藏边缘图与主图共享的数据轴上的刻度线，但保留其独立的频率/密度轴上的刻度线。这体现了将优秀的制图实践固化到组件内部的设计思想。

7.  **稳健的缺失值可视化 (Robust Visualization of Missing Data)**:
    *   **问题**: 在科学可视化中，明确地展示数据缺失的位置和数量至关重要。简单地忽略NaN值可能会误导分析。
    *   **解决方案**: 当用户为颜色轴数据配置了 `nan_color` 时，`_draw_main_trace` 方法会启动**“双轨迹策略” (Dual-Trace Strategy)**。它会：
        1.  绘制一条轨迹，包含所有具有有效Z值的点，并应用颜色映射。
        2.  绘制第二条独立的轨迹，专门用于显示所有Z值为NaN的数据点，并为其应用指定的`nan_color`。
    *   **优势**: 这种方法不仅避免了因NaN值导致颜色映射失败，更重要的是，它以一种清晰、无歧义的方式，在图表上直观地呈现了数据的完整性状况。

8.  **通过精细化配置暴露原生能力 (Exposing Native Power via Granular Configuration)**:
    *   **设计哲学**: 我们没有对Plotly的功能进行删减或过度简化，而是选择了一条更强大的路径：在`config.py`中，我们通过Pydantic模型，几乎**一对一地映射了Plotly 6.0+中所有与交会图相关的核心配置项**。从`AxisConfig`中的刻度格式，到`ColorbarConfig`中的边框宽度，再到`MarkerConfig`中的高级尺寸控制，几乎所有Plotly原生支持的属性都可以在我们的配置模型中找到对应的字段。
    *   **架构优势**:
        *   **最大化能力**: 这种设计确保了用户不会因为我们的封装而失去对底层库强大功能的访问。任何Plotly能做到的精细调整，我们的组件同样能做到。
        *   **未来的可扩展性**: 当Plotly发布新功能时，我们只需在相应的Pydantic模型中增加一个新字段，即可轻松地将新功能集成进来，而无需重构核心绘图逻辑。
        *   **类型安全的“Plotly字典”**: 对于熟悉Plotly的用户来说，`CrossPlotConfig`可以被看作是一个类型安全、带自动补全和文档说明的、结构化的Plotly `layout`和`trace`配置字典。这极大地降低了因手写复杂字典而导致的拼写错误或结构错误。
    *   **结论**: 这种设计体现了“智能包装”的最高境界：在提供高层便捷API（如`presets`）的同时，也为高级用户保留了通往底层全部功能的、安全且结构化的“快车道”。

---

## 2. API 使用指南与示例

### 2.1. 步骤门面 (Step Facade) - 标准工作流用法

`run_crossplot_step` 是与工作流集成的标准入口。它处理 `logwp` 的核心数据模型，自动解析曲线名，并管理所有产物的保存与注册。

**场景**: 在一个完整的工作流中，作为下游步骤消费上游计算结果，生成一份用于报告的、出版物级别的渗透率-孔隙度交会图。

```python
# 在一个工作流脚本中 (e.g., run_experiment.py)
from logwp.extras.tracking import RunContext
from logwp.models.datasets.bundle import WpDataFrameBundle
from logwp.extras.petroplot.crossplot import (
    run_crossplot_step,
    create_publication_single_series_scatter_config
)

# 假设 train_bundle 是上游步骤产出的 WpDataFrameBundle
# train_bundle.data 包含 'POR', 'PERM', 'VSH' 等物理列

with RunContext(run_dir="./my_run") as ctx:
    # 1. 使用便捷函数快速创建一份高质量的配置
    # 该函数封装了大量出版物级别的默认设置
    crossplot_config = create_publication_single_series_scatter_config(
        # 数据选择
        x_curve="POR",
        y_curve="PERM",
        z_curve="VSH",
        # X轴配置
        x_title="Porosity (%)",
        x_range_min=0,
        x_range_max=30,
        # Y轴配置 (对数)
        y_title="Permeability (mD)",
        y_range_min=0.01,
        y_range_max=10000,
        y_log=True,
        # 颜色轴配置
        z_title="Vshale",
        z_cmin=0,
        z_cmax=1,
        z_cmap="Viridis",
        # 特征
        show_marginals=True,
        # 导出设置
        export_width_inches=5,
        export_height_inches=5,
        export_dpi=300
    )

    # 2. 执行步骤
    results = run_crossplot_step(
        config=crossplot_config,
        ctx=ctx,
        bundles={"default": train_bundle}, # 传入包含元数据和数据的数据包字典
        prefix="final_report" # 保证产物名称唯一
    )

    print("步骤完成，产物已保存在 './my_run/final_report_crossplot' 目录中。")
```

### 2.2. 重绘函数 (Replot Function) - 复现与调试

`replot_crossplot_from_snapshot` 可以从 `run_*` 步骤生成的快照文件中精确地复现图表。

**场景**: 调试图表样式、修改标签或重新生成不同格式的图表，而无需重新运行上游的耗时计算。

```python
from pathlib import Path
from logwp.extras.plotting import registry
from logwp.extras.petroplot.crossplot import replot_crossplot_from_snapshot

# 假设这些文件由 `run_crossplot_step` 生成
snapshot_dir = Path("./my_run/final_report_crossplot/data_sources")
logic_config_path = Path("./my_run/final_report_crossplot/logic_config.json")

# 1. 获取一个绘图模板 (可以与原始模板不同，以改变样式)
style = registry.get("petroplot.crossplot.default")

# 2. 调用重绘函数
replot_crossplot_from_snapshot(
    snapshot_dir=snapshot_dir,
    logic_config_path=logic_config_path,
    plot_style=style,
    output_path=Path("./replot_figure.pdf") # 输出为PDF格式
)

print("图表已从快照成功复现并保存为 'replot_figure.pdf'")
```

---

## 3. 配置详解

本组件的灵活性主要来源于其强大的配置系统 `CrossPlotConfig`。

### 3.1. 核心配置模型

| 配置模型 | 作用 |
| :--- | :--- |
| `CrossPlotConfig` | **顶层配置对象**。聚合了所有其他配置，是传递给 `run_crossplot_step` 的主要参数。 |
| `SeriesConfig` | 定义一个**数据系列**。包括其数据来源（x, y, z曲线）、可视化类型（散点、线）等。 |
| `AxisConfig` | 定义一个**坐标轴**。包括其标题、范围、刻度类型（线性/对数）、刻度间隔和网格线。 |
| `ColorbarConfig` | 定义**颜色轴**。包括其标题、范围、颜色映射表（cmap）、是否对数变换等。 |
| `MarginalConfig` | 定义**边缘图**。包括是否启用、类型（直方图/箱线图等）、尺寸等。 |
| `ExportConfig` | 定义**导出设置**。包括图像尺寸（英寸）和分辨率（DPI）。 |
| `PlotStyle` | 定义**美学样式**。控制所有与颜色、字体、线宽相关的视觉表现。 |

### 3.2. 便捷预设函数

为了简化常见场景的配置，我们提供了 `create_publication_single_series_scatter_config` 函数。它是一个“工厂函数”，接收描述图表核心要素的简单参数，并返回一个内部填充了大量高质量默认值的、完整的 `CrossPlotConfig` 对象。

**强烈建议**在需要绘制单系列散点图时，优先使用此函数作为起点，而不是从零开始构建 `CrossPlotConfig`。

---

## 4. `petroplot` 包目录结构

`crossplot` 组件是 `petroplot` 系列的一部分，其目录结构清晰地反映了关注点分离的设计原则。

```text
logwp/extras/petroplot/
├── crossplot/            # 【特定实现】交会图组件
│   ├── __init__.py       # 导出公共API
│   ├── facade.py         # 实现三层API函数
│   ├── config.py         # 定义 CrossPlotConfig 等特有配置
│   ├── presets.py        # 提供便捷的配置“工厂函数”
│   ├── internal/
│   │   └── plotter.py    # 核心绘图引擎 (将Config翻译为Plotly对象)
│   └── ...               # (constants.py, plot_profiles.py, plotting.py)
└── ...                   # (其他petroplot组件，如nmr_ternary)
```
