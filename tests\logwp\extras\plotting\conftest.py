"""测试配置和共享fixtures。

提供绘图系统测试所需的共享fixtures和配置。
"""

import tempfile
from pathlib import Path
from typing import Generator

import pytest

from logwp.extras.plotting import PlotProfile, PlotStyle, SaveConfig, PlottingConfigRegistry


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """创建临时目录用于测试文件操作。"""
    with tempfile.TemporaryDirectory() as tmp_dir:
        yield Path(tmp_dir)


@pytest.fixture
def clean_registry() -> Generator[PlottingConfigRegistry, None, None]:
    """提供一个干净的注册表实例用于测试。"""
    registry = PlottingConfigRegistry()
    yield registry
    # 测试后清理不需要，因为每个测试都会创建新实例


@pytest.fixture
def sample_save_config() -> SaveConfig:
    """提供示例保存配置。"""
    return SaveConfig(
        format="png",
        dpi=150,
        width=8.0,
        height=6.0,
        transparent=True
    )


@pytest.fixture
def sample_plot_profile() -> PlotProfile:
    """提供示例绘图配置。"""
    return PlotProfile(
        name="test.sample",
        rc_params={
            "font.size": 12,
            "axes.grid": True,
            "grid.alpha": 0.5
        },
        figure_props={
            "figsize": (10, 8),
            "dpi": 100
        },
        title_props={
            "label": "Sample Plot",
            "fontsize": 16,
            "fontweight": "bold"
        },
        label_props={
            "xlabel": "X Axis",
            "ylabel": "Y Axis",
            "fontsize": 12
        },
        artist_props={
            "color": "blue",
            "linewidth": 2,
            "alpha": 0.8
        }
    )


@pytest.fixture
def sample_plot_style() -> PlotStyle:
    """提供示例PlotStyle配置。"""
    return PlotStyle(
        name="test.sample",  # Use same name as sample_plot_profile for testing
        font={"family": "Verdana", "size_title": 20},
        color={
            "cycle": ["#ff0000", "#00ff00", "#0000ff"],
            "background_plot": "#f0f0f0",
        },
        frame={"color": "#333333", "width": 1.5},
    )


@pytest.fixture
def global_base_profile() -> PlotProfile:
    """提供全局基础模板。"""
    return PlotProfile(
        name="base",
        rc_params={
            "font.family": "Arial",
            "font.size": 10,
            "figure.facecolor": "white"
        },
        figure_props={
            "dpi": 150,
            "layout": "constrained"
        }
    )


@pytest.fixture
def module_base_profile() -> PlotProfile:
    """提供模块级基础模板。"""
    return PlotProfile(
        name="test_module.base",
        rc_params={
            "font.family": "serif",
            "axes.grid": True
        },
        figure_props={
            "figsize": (8, 8)
        }
    )


@pytest.fixture
def specific_profile() -> PlotProfile:
    """提供具体图表配置。"""
    return PlotProfile(
        name="test_module.specific_chart",
        title_props={
            "label": "Specific Chart",
            "fontsize": 18
        },
        artist_props={
            "color": "red",
            "marker": "o"
        }
    )


@pytest.fixture
def matplotlib_figure():
    """提供matplotlib图表用于测试。"""
    pytest.importorskip("matplotlib")
    import matplotlib
    matplotlib.use('Agg')  # 使用非交互式后端
    import matplotlib.pyplot as plt

    fig, ax = plt.subplots(figsize=(6, 4))
    ax.plot([1, 2, 3], [1, 4, 2], label="test data")

    yield fig, ax

    plt.close(fig)


@pytest.fixture
def sample_json_config() -> dict:
    """提供示例JSON配置数据。"""
    return {
        "name": "json_test.profile",
        "save_config": {
            "format": "svg",
            "dpi": 300,
            "transparent": False,
            "bbox_inches": "tight"
        },
        "rc_params": {
            "font.size": 14,
            "axes.grid": True
        },
        "figure_props": {
            "figsize": [12, 8],
            "dpi": 200
        },
        "title_props": {
            "label": "JSON Test Plot",
            "fontsize": 20
        },
        "label_props": {
            "xlabel": "JSON X",
            "ylabel": "JSON Y"
        },
        "artist_props": {
            "color": "green",
            "linewidth": 3
        }
    }
