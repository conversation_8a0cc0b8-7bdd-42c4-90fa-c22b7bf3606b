"""logwp.extras.petroplot.crossplot.plotting - 交会图复现功能

提供从数据快照重新生成图表的功能。
"""
from __future__ import annotations

from pathlib import Path

import plotly.graph_objects as go

from logwp.extras.plotting import PlotStyle
from logwp.infra import get_logger

from .artifact_handler import CrossPlotArtifactHandler
from .config import CrossPlotConfig, CrossPlotColumnSelectors
from .internal.plotter import CrossPlotter

logger = get_logger(__name__)


def replot_crossplot(
    snapshot_dir: Path,
    logic_config_path: Path,
    plot_style: PlotStyle,
    output_path: Path,
) -> None:
    """从快照文件精确复现交会图。"""
    logger.info(
        "开始从快照复现交会图",
        snapshot_dir=str(snapshot_dir),
        logic_config_path=str(logic_config_path),
        output_path=str(output_path),
    )

    # 1. Load artifacts using the handler
    handler = CrossPlotArtifactHandler()
    logic_config_data = handler.load_logic_config(logic_config_path)
    config = CrossPlotConfig(**logic_config_data['config'])
    selectors = CrossPlotColumnSelectors(**logic_config_data['selectors'])

    # Load all necessary dataframes from the snapshot directory
    data_dict = {}
    all_bundle_names = {s.bundle_name for s in selectors.series}
    for bundle_name in all_bundle_names:
        csv_path = snapshot_dir / f"{bundle_name}.csv"
        if csv_path.exists():
            data_dict[bundle_name] = handler.load_dataframe(csv_path)
        else:
            logger.warning(f"数据快照文件未找到，已跳过: {csv_path}")

    # 2. Inject the new plot style into the loaded configuration
    config.style = plot_style

    # 3. Instantiate the plotter and create the plot
    plotter = CrossPlotter(config)
    fig = plotter.create_plot(data_dict, selectors)

    # 4. Save the output figure
    output_path.parent.mkdir(parents=True, exist_ok=True)
    # Use plotly's native format inference from file extension
    fig.write_image(output_path)
    logger.info(f"图表已从快照成功复现并保存至: {output_path}")
