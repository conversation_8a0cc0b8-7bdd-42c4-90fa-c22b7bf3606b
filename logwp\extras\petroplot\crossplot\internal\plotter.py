

import plotly.graph_objects as go
import pandas as pd
from plotly.subplots import make_subplots
import numpy as np
import itertools
from typing import List, Dict, Any, Optional, Union, Callable

from ..config import (
    CrossPlotConfig, CrossPlotColumnSelectors, LineConfig, MarginalEnabled,
    MarginalKind, MarkerConfig, ScaleType, SeriesConfig, SeriesColumnSelectors,
    SeriesType, AxisConfig, ReferenceLineConfig
)
from .marginal_plot_helper import MARGINAL_DRAWERS


class CrossPlotter:
    """Main class for creating cross-plots with marginal distributions."""

    def __init__(self, config: Union[CrossPlotConfig, Dict[str, Any]]):
        """Initialize the cross-plotter with configuration."""
        if isinstance(config, dict):
            self.config = CrossPlotConfig(**config)
        else:
            self.config = config

    def create_plot(self, data_dict: Dict[str, pd.DataFrame], selectors: CrossPlotColumnSelectors) -> go.Figure:
        """Create a cross-plot figure from the provided data."""
        # Create subplot layout
        fig = self._create_subplot_layout()

        # Pre-calculate logarithmic bins if needed
        log_bins_x, log_bins_y = self._calculate_logarithmic_bins(data_dict, selectors)

        # Draw main traces and marginal plots
        self._draw_traces(fig, data_dict, selectors, log_bins_x, log_bins_y)

        # Apply layout and styling
        self._apply_layout_and_styling(fig)

        # Add reference line if requested
        if self.config.reference_line:
            self._add_reference_line(fig, data_dict, selectors)

        return fig

    def _create_subplot_layout(self) -> go.Figure:
        """Create the subplot layout based on marginal configuration."""
        has_marginal_x = self.config.marginal.enabled in [MarginalEnabled.X, MarginalEnabled.BOTH]
        has_marginal_y = self.config.marginal.enabled in [MarginalEnabled.Y, MarginalEnabled.BOTH]

        # Get configurable sizes
        marginal_x_size = self.config.marginal.size_x
        marginal_y_size = self.config.marginal.size_y
        spacing = self.config.marginal.spacing

        # Calculate main plot sizes
        main_width = 1.0 - marginal_y_size if has_marginal_y else 1.0
        main_height = 1.0 - marginal_x_size if has_marginal_x else 1.0

        if has_marginal_x and has_marginal_y:
            # 2x2 grid: marginal X (top), marginal Y (right), main (bottom-left)
            fig = make_subplots(
                rows=2, cols=2,
                column_widths=[main_width, marginal_y_size],
                row_heights=[marginal_x_size, main_height],
                specs=[[{"type": "xy"}, {"type": "xy"}],
                       [{"type": "scatter"}, {"type": "xy"}]],
                horizontal_spacing=spacing, vertical_spacing=spacing,
                shared_xaxes='columns', shared_yaxes='rows'
            )
        elif has_marginal_x:
            # 2x1 grid: marginal X (top), main (bottom)
            fig = make_subplots(
                rows=2, cols=1, row_heights=[marginal_x_size, main_height],
                specs=[[{"type": "xy"}], [{"type": "scatter"}]],
                vertical_spacing=spacing, shared_xaxes=True
            )
        elif has_marginal_y:
            # 1x2 grid: main (left), marginal Y (right)
            fig = make_subplots(
                rows=1, cols=2, column_widths=[main_width, marginal_y_size],
                specs=[[{"type": "scatter"}, {"type": "xy"}]],
                horizontal_spacing=spacing, shared_yaxes=True
            )
        else:
            # 1x1 grid: main plot only
            fig = make_subplots(rows=1, cols=1, specs=[[{"type": "scatter"}]])

        return fig

    def _calculate_logarithmic_bins(self, data_dict: Dict[str, pd.DataFrame],
                                    selectors: CrossPlotColumnSelectors) -> tuple:
        """Calculate logarithmic bins for marginal plots if needed.

        Note: Only histogram marginal plots require special logarithmic binning.
        Box, Violin, and KDE plots work natively with log scales in Plotly 6.0+.
        """
        has_marginal_x = self.config.marginal.enabled in [MarginalEnabled.X, MarginalEnabled.BOTH]
        has_marginal_y = self.config.marginal.enabled in [MarginalEnabled.Y, MarginalEnabled.BOTH]

        all_x_series = []
        all_y_series = []
        for s in selectors.series:
            df = data_dict.get(s.bundle_name)
            if df is not None and not df.empty:
                if s.x_col and s.x_col in df.columns:
                    all_x_series.append(df[s.x_col])
                if s.y_col and s.y_col in df.columns:
                    all_y_series.append(df[s.y_col])

        log_bins_x = None
        if (has_marginal_x and
            self.config.xaxis.scale == ScaleType.LOG and
            self.config.marginal.kind == MarginalKind.HIST):

            if all_x_series:
                all_x_vals = pd.concat(all_x_series).dropna()
                all_x_vals = all_x_vals[all_x_vals > 0]
                num_bins = self.config.marginal.bins if isinstance(self.config.marginal.bins, int) else 25
                min_val = np.log10(all_x_vals.min())
                max_val = np.log10(all_x_vals.max())
                log_bins_x = np.logspace(min_val, max_val, num_bins)

        log_bins_y = None
        if (has_marginal_y and
            self.config.yaxis.scale == ScaleType.LOG and
            self.config.marginal.kind == MarginalKind.HIST):
            if all_y_series:
                all_y_vals = pd.concat(all_y_series).dropna()
                all_y_vals = all_y_vals[all_y_vals > 0]
                num_bins = self.config.marginal.bins if isinstance(self.config.marginal.bins, int) else 25
                min_val = np.log10(all_y_vals.min())
                max_val = np.log10(all_y_vals.max())
                log_bins_y = np.logspace(min_val, max_val, num_bins)

        return log_bins_x, log_bins_y

    def _draw_traces(self, fig: go.Figure,
                     data_dict: Dict[str, pd.DataFrame],
                     selectors: CrossPlotColumnSelectors,
                     log_bins_x: Optional[np.ndarray], log_bins_y: Optional[np.ndarray]) -> None:
        """Draw all traces (main and marginal) on the figure."""
        # Get color cycle
        color_cycle = itertools.cycle(self.config.style.color.get("cycle", []))

        # Determine subplot positions
        has_marginal_x = self.config.marginal.enabled in [MarginalEnabled.X, MarginalEnabled.BOTH]
        has_marginal_y = self.config.marginal.enabled in [MarginalEnabled.Y, MarginalEnabled.BOTH]

        main_row, main_col = self._get_main_subplot_position(has_marginal_x, has_marginal_y)

        # Track colorbar display (only for global colorbar)
        global_colorbar_shown = False

        for series_selector in selectors.series:
            df = data_dict.get(series_selector.bundle_name)
            if df is None or df.empty:
                continue

            # 确保所有需要的列都存在并移除NaN
            required_cols = [c for c in [series_selector.x_col, series_selector.y_col] if c]
            if not all(c in df.columns for c in required_cols):
                continue
            df_clean = df.dropna(subset=required_cols)
            if df_clean.empty:
                continue

            # Get or create series configuration
            series_config = self._get_series_config(series_selector, color_cycle)

            # Extract data arrays
            x_vals = df_clean[series_selector.x_col]
            y_vals = df_clean[series_selector.y_col]
            z_vals = df_clean[series_selector.z_col] if series_selector.z_col and series_selector.z_col in df_clean.columns else pd.Series(dtype=float)
            error_x_vals = df_clean[series_selector.error_x_col] if series_selector.error_x_col and series_selector.error_x_col in df_clean.columns else pd.Series(dtype=float)
            error_y_vals = df_clean[series_selector.error_y_col] if series_selector.error_y_col and series_selector.error_y_col in df_clean.columns else pd.Series(dtype=float)

            # 判断是否显示全局颜色轴（仅当没有系列独立颜色轴时）
            has_series_colorbar = series_config.colorbar and series_config.colorbar.visible and not z_vals.empty
            show_global_colorbar = (not global_colorbar_shown and
                                  not has_series_colorbar and
                                  self.config.colorbar.visible and not z_vals.empty)

            # Draw main trace
            self._draw_main_trace(fig, series_config, x_vals, y_vals, z_vals,
                                error_x_vals, error_y_vals, main_row, main_col,
                                show_global_colorbar)

            if show_global_colorbar:
                global_colorbar_shown = True

            # Draw marginal traces
            self._draw_marginal_traces(fig, series_config, x_vals, y_vals,
                                     has_marginal_x, has_marginal_y,
                                     log_bins_x, log_bins_y, main_col, main_row)

    def _get_main_subplot_position(self, has_marginal_x: bool, has_marginal_y: bool) -> tuple:
        """Get the row and column position of the main subplot."""
        if has_marginal_x and has_marginal_y:
            return (2, 1)  # Bottom-left in 2x2 grid
        elif has_marginal_x:
            return (2, 1)  # Bottom in 2x1 grid
        elif has_marginal_y:
            return (1, 1)  # Left in 1x2 grid
        else:
            return (1, 1)  # Only subplot in 1x1 grid

    def _get_series_config(self, series_selector: SeriesColumnSelectors, color_cycle: itertools.cycle) -> SeriesConfig:
        """Get or create series configuration with color assignment."""
        # Look for existing series configuration
        for series_config in self.config.series:
            if series_config.id == series_selector.id:
                # Apply color from cycle if not explicitly set
                if series_config.marker.facecolor is None:
                    series_config.marker.facecolor = next(color_cycle)
                return series_config

        # Create new series configuration with default values
        # This case should ideally not be hit if selectors are derived from config
        default_color = next(color_cycle)
        return SeriesConfig(
            id=series_selector.id,
            marker=MarkerConfig(facecolor=default_color),
            line=LineConfig(color=default_color)
        )

    def _draw_main_trace(self, fig: go.Figure, series_config: SeriesConfig,
                        x_vals: pd.Series, y_vals: pd.Series, z_vals: pd.Series,
                        error_x_vals: pd.Series, error_y_vals: pd.Series,
                        row: int, col: int, show_colorbar: bool) -> None:
        """Draw the main scatter/line trace using dual-trace strategy for NaN handling."""

        # R-12 NaN处理：数据分离策略
        if not z_vals.empty:
            # 分离有效数据和NaN数据
            valid_mask = z_vals.notna()

            # 关键修复：先绘制NaN数据轨迹，将其置于背景层
            nan_color = self._get_nan_color(series_config)
            if nan_color and not valid_mask.all():
                nan_x = x_vals[~valid_mask]
                nan_y = y_vals[~valid_mask]
                self._draw_nan_trace(fig, series_config, nan_x, nan_y, nan_color, row, col)

            # 再绘制有效数据轨迹，使其显示在顶层
            if valid_mask.any():
                valid_x = x_vals[valid_mask]
                valid_y = y_vals[valid_mask]
                valid_z = z_vals[valid_mask]
                valid_error_x = error_x_vals[valid_mask] if not error_x_vals.empty else pd.Series(dtype=float)
                valid_error_y = error_y_vals[valid_mask] if not error_y_vals.empty else pd.Series(dtype=float)

                self._draw_valid_trace(fig, series_config, valid_x, valid_y, valid_z,
                                     valid_error_x, valid_error_y, row, col, show_colorbar)

        else:
            # 无Z值数据，使用原有逻辑
            self._draw_valid_trace(fig, series_config, x_vals, y_vals, [],
                                 error_x_vals, error_y_vals, row, col, show_colorbar)

    def _get_nan_color(self, series_config: SeriesConfig) -> Optional[str]:
        """获取NaN值显示颜色，优先级：系列配置 > 全局配置"""
        if series_config.colorbar and hasattr(series_config.colorbar, 'nan_color'):
            return series_config.colorbar.nan_color
        return getattr(self.config.colorbar, 'nan_color', None)

    def _draw_valid_trace(self, fig: go.Figure, series_config: SeriesConfig,
                         x_vals: pd.Series, y_vals: pd.Series, z_vals: pd.Series,
                         error_x_vals: pd.Series, error_y_vals: pd.Series,
                         row: int, col: int, show_colorbar: bool) -> None:
        """绘制有效数据轨迹（原有逻辑，零修改）"""
        # Determine trace mode based on series type
        mode = self._get_trace_mode(series_config.type)

        # Build trace arguments
        trace_args = {
            'x': x_vals,
            'y': y_vals,
            'mode': mode,
            'name': series_config.id,
            'legendgroup': series_config.id,
            'marker': {
                'symbol': series_config.marker.symbol,
                'size': series_config.marker.size,
                'opacity': series_config.marker.opacity,
                'line': {
                    'color': series_config.marker.linecolor,
                    'width': series_config.marker.edgewidth
                }
            },
            'line': {
                'color': series_config.line.color or series_config.marker.facecolor,
                'width': series_config.line.width,
                'dash': self._convert_line_style(series_config.line.style)
            }
        }

        # Handle color mapping (highest priority)
        # 优先检查系列独立的颜色轴配置
        series_colorbar = series_config.colorbar
        use_series_colorbar = series_colorbar and series_colorbar.visible and not z_vals.empty
        use_global_colorbar = self.config.colorbar.visible and not z_vals.empty and not use_series_colorbar

        if use_series_colorbar:
            # 使用系列独立的颜色轴配置
            trace_args['marker']['color'] = z_vals
            trace_args['marker']['colorscale'] = series_config.colorscale or series_colorbar.cmap
            trace_args['marker']['showscale'] = True  # 每个系列都显示自己的颜色轴

            # Convert orientation to Plotly format
            orientation = 'v' if series_colorbar.orientation == 'vertical' else 'h'
            colorbar_config = {
                'title': series_colorbar.title,
                'orientation': orientation,
                'thickness': series_colorbar.thickness,
                'len': series_colorbar.len
            }

            # 设置颜色轴位置
            if orientation == 'v':
                if series_colorbar.x is not None:
                    colorbar_config['x'] = series_colorbar.x
                if series_colorbar.y is not None:
                    colorbar_config['y'] = series_colorbar.y
                if series_colorbar.yanchor:
                    colorbar_config['yanchor'] = series_colorbar.yanchor
                if series_colorbar.xanchor:
                    colorbar_config['xanchor'] = series_colorbar.xanchor
            else:
                if series_colorbar.x is not None:
                    colorbar_config['x'] = series_colorbar.x
                if series_colorbar.y is not None:
                    colorbar_config['y'] = series_colorbar.y
                if series_colorbar.xanchor:
                    colorbar_config['xanchor'] = series_colorbar.xanchor
                if series_colorbar.yanchor:
                    colorbar_config['yanchor'] = series_colorbar.yanchor

            trace_args['marker']['colorbar'] = colorbar_config

            if series_colorbar.clim:
                trace_args['marker']['cmin'] = series_colorbar.clim[0]
                trace_args['marker']['cmax'] = series_colorbar.clim[1]

        elif use_global_colorbar:
            # 使用全局颜色轴配置
            trace_args['marker']['color'] = z_vals
            trace_args['marker']['colorscale'] = self.config.colorbar.cmap
            trace_args['marker']['showscale'] = show_colorbar
            if show_colorbar:
                # Convert orientation to Plotly format
                orientation = 'v' if self.config.colorbar.orientation == 'vertical' else 'h'
                colorbar_config = {
                    'title': self.config.colorbar.title,
                    'orientation': orientation
                }

                # 使用Plotly的智能布局，只在必要时手动调整
                if orientation == 'v':
                    # 垂直颜色轴：使用默认位置，只调整厚度
                    colorbar_config.update({
                        'thickness': 15,  # 使其更薄
                        'len': 0.8  # 稍短一些
                    })
                else:
                    # 水平颜色轴：使用默认位置
                    colorbar_config.update({
                        'thickness': 15,
                        'len': 0.8
                    })

                trace_args['marker']['colorbar'] = colorbar_config

                if self.config.colorbar.clim:
                    trace_args['marker']['cmin'] = self.config.colorbar.clim[0]
                    trace_args['marker']['cmax'] = self.config.colorbar.clim[1]
        else:
            # Use series-specific color or default
            trace_args['marker']['color'] = series_config.marker.facecolor

        # Add error bars if configured and data available
        if (self.config.error_bars.visible or series_config.error_bars.visible) and not error_x_vals.empty:
            trace_args['error_x'] = {
                'type': 'data',
                'array': error_x_vals,
                'visible': True,
                'color': series_config.error_bars.color or series_config.marker.facecolor,
                'thickness': series_config.error_bars.thickness
            }

        if (self.config.error_bars.visible or series_config.error_bars.visible) and not error_y_vals.empty:
            trace_args['error_y'] = {
                'type': 'data',
                'array': error_y_vals,
                'visible': True,
                'color': series_config.error_bars.color or series_config.marker.facecolor,
                'thickness': series_config.error_bars.thickness
            }

        # Add trace to figure
        fig.add_trace(go.Scatter(**trace_args), row=row, col=col)

    def _draw_nan_trace(self, fig: go.Figure, series_config: SeriesConfig,
                       x_vals: pd.Series, y_vals: pd.Series, nan_color: str,
                       row: int, col: int) -> None:
        """绘制NaN数据点轨迹"""
        # 检查是否显示NaN轨迹的图例
        show_legend = False

        # 优先级：系列配置 > 全局配置
        if series_config.colorbar and hasattr(series_config.colorbar, 'nan_show_legend'):
            show_legend = series_config.colorbar.nan_show_legend
        else:
            show_legend = getattr(self.config.colorbar, 'nan_show_legend', False)

        # 获取自定义图例文字，优先级：系列配置 > 全局配置 > 默认格式
        legend_text = None
        if series_config.colorbar and hasattr(series_config.colorbar, 'nan_legend_text'):
            legend_text = series_config.colorbar.nan_legend_text
        else:
            legend_text = getattr(self.config.colorbar, 'nan_legend_text', None)

        # 如果没有自定义文字，使用默认格式
        if legend_text is None:
            legend_text = f"{series_config.id} (NaN)"

        fig.add_trace(go.Scatter(
            x=x_vals, y=y_vals,
            mode='markers',
            marker=dict(
                color=nan_color,
                size=series_config.marker.size,
                symbol=series_config.marker.symbol,
                opacity=series_config.marker.opacity,
                line=dict(
                    color=series_config.marker.linecolor,
                    width=series_config.marker.edgewidth
                )
            ),
            name=legend_text,
            legendgroup=series_config.id,
            showlegend=show_legend,
            hovertemplate='x: %{x}<br>y: %{y}<br>z: NaN<extra></extra>'
        ), row=row, col=col)

    def _get_trace_mode(self, series_type: SeriesType) -> str:
        """Convert series type to Plotly trace mode."""
        if series_type == SeriesType.SCATTER:
            return 'markers'
        elif series_type == SeriesType.LINE:
            return 'lines'
        elif series_type == SeriesType.BOTH:
            return 'markers+lines'
        else:
            return 'markers'

    def _convert_line_style(self, style: str) -> str:
        """Convert line style to Plotly dash format."""
        style_map = {
            'solid': 'solid',
            'dash': 'dash',
            'dot': 'dot',
            'dashdot': 'dashdot'
        }
        return style_map.get(style, 'solid')

    def _draw_marginal_traces(self, fig: go.Figure, series_config: SeriesConfig,
                            x_vals: pd.Series, y_vals: pd.Series,
                            has_marginal_x: bool, has_marginal_y: bool,
                            log_bins_x: Optional[np.ndarray], log_bins_y: Optional[np.ndarray],
                            main_col: int, main_row: int) -> None:
        """Draw marginal traces for the current series."""
        drawer = MARGINAL_DRAWERS.get(self.config.marginal.kind)
        if not drawer:
            print(f"Warning: No drawer found for marginal kind '{self.config.marginal.kind}'")
            return

        # Draw X marginal (top)
        if has_marginal_x:
            drawer.draw(
                fig=fig,
                data=x_vals,
                series_config=series_config,
                marginal_config=self.config.marginal,
                row=1,
                col=main_col,
                orientation='vertical',
                bins=log_bins_x
            )

        # Draw Y marginal (right)
        if has_marginal_y:
            y_col = 2 if has_marginal_x else 2  # Always column 2 for Y marginal
            drawer.draw(
                fig=fig,
                data=y_vals,
                series_config=series_config,
                marginal_config=self.config.marginal,
                row=main_row,
                col=y_col,
                orientation='horizontal',
                bins=log_bins_y
            )

    def _apply_layout_and_styling(self, fig: go.Figure) -> None:
        """Apply layout configuration and styling to the figure."""
        # Determine main axis names based on subplot layout
        has_marginal_x = self.config.marginal.enabled in [MarginalEnabled.X, MarginalEnabled.BOTH]
        has_marginal_y = self.config.marginal.enabled in [MarginalEnabled.Y, MarginalEnabled.BOTH]

        main_xaxis_name, main_yaxis_name = self._get_main_axis_names(has_marginal_x, has_marginal_y)

        # Build layout updates
        layout_updates = {
            'title': {
                'text': self.config.figure.title,
                'font': {
                    'family': self.config.style.font.get("family", "Arial"),
                    'size': self.config.style.font.get("size_title", 14),
                    'color': self.config.style.font.get("color", "#333333")
                }
            },
            'plot_bgcolor': self.config.style.color.get("background_plot", "#ffffff"),
            'paper_bgcolor': self.config.style.color.get("background_canvas", "#ffffff"),
            'font': {
                'family': self.config.style.font.get("family", "Arial"),
                'color': self.config.style.font.get("color", "#333333")
            },
            'legend': self._get_legend_config(),
            'width': self.config.figure.size[0],
            'height': self.config.figure.size[1]
        }

        # Configure main axes
        layout_updates[main_xaxis_name] = self._build_axis_config(self.config.xaxis, 'x')
        layout_updates[main_yaxis_name] = self._build_axis_config(self.config.yaxis, 'y')

        # Apply layout updates
        fig.update_layout(**layout_updates)

        # Apply global axis styling
        self._apply_global_axis_styling(fig)

        # Ensure marginal axes match main axes for logarithmic scales
        self._sync_marginal_axes(fig, has_marginal_x, has_marginal_y)

        # Set barmode for histograms
        if self.config.marginal.kind == MarginalKind.HIST:
            fig.update_layout(barmode='overlay' if self.config.marginal.overlay == 'overlay' else 'group')

    def _get_main_axis_names(self, has_marginal_x: bool, has_marginal_y: bool) -> tuple:
        """Get the axis names for the main subplot."""
        if has_marginal_x and has_marginal_y:
            return 'xaxis3', 'yaxis3'  # Bottom-left in 2x2 grid
        elif has_marginal_x:
            return 'xaxis2', 'yaxis2'  # Bottom in 2x1 grid
        else:
            return 'xaxis', 'yaxis'    # Main axes in 1x2 or 1x1 grid

    def _build_axis_config(self, axis_config: AxisConfig, axis_type: str) -> Dict[str, Any]:
        """Build axis configuration dictionary with advanced tick control."""
        # Convert scale type to Plotly-compatible format
        scale_type = self._convert_scale_type(axis_config.scale)

        config = {
            'title': {
                'text': axis_config.title.text,
                'font': {
                    'size': self.config.style.font.get("size_label", 12),
                    'color': self.config.style.font.get("color", "#333333")
                }
            },
            'type': scale_type,
            'autorange': 'reversed' if axis_config.inverted else True,
            'rangemode': axis_config.range.mode,
            'tickfont': {'size': self.config.style.font.get("size_ticks", 10)},

            # 主网格线配置（样式从StyleConfig获取）
            'showgrid': axis_config.grid.major_enabled,
            'gridcolor': self.config.style.color.get("grid_major", "#bbbbbb"),
            'gridwidth': self.config.style.linewidth.get("grid_major", 0.6),
            'griddash': axis_config.grid.major_dash,

            # 主刻度配置（样式从StyleConfig获取）
            'ticks': axis_config.ticks.position,
            'ticklen': self.config.style.tick.get("major_length", 6),
            'tickwidth': self.config.style.linewidth.get("tick_major", 1.0),
            'tickcolor': self.config.style.color.get("tick_major", "#666666"),
        }

        # 刻度模式配置
        if axis_config.ticks.mode == "linear" and axis_config.ticks.interval is not None:
            config['tickmode'] = 'linear'
            config['dtick'] = axis_config.ticks.interval
            if axis_config.ticks.start is not None:
                config['tick0'] = axis_config.ticks.start

        # 次要刻度配置（样式从StyleConfig获取）
        if axis_config.ticks.minor_enabled:
            minor_config = {
                'ticks': axis_config.ticks.minor_position,
                'ticklen': self.config.style.tick.get("minor_length", 4),
                'tickcolor': self.config.style.color.get("tick_minor", "#999999"),
                'showgrid': axis_config.grid.minor_enabled,
            }

            if axis_config.grid.minor_enabled:
                minor_config.update({
                    'gridcolor': self.config.style.color.get("grid_minor", "#dddddd"),
                    'gridwidth': self.config.style.linewidth.get("grid_minor", 0.4),
                    'griddash': axis_config.grid.minor_dash,
                })

            if axis_config.ticks.minor_interval is not None:
                minor_config['dtick'] = axis_config.ticks.minor_interval
                if axis_config.ticks.minor_start is not None:
                    minor_config['tick0'] = axis_config.ticks.minor_start

            config['minor'] = minor_config

        # 手动范围设置
        if axis_config.range.min_value is not None or axis_config.range.max_value is not None:
            range_values = []
            if axis_config.range.min_value is not None:
                range_values.append(axis_config.range.min_value)
            if axis_config.range.max_value is not None:
                if len(range_values) == 0:
                    range_values = [None, axis_config.range.max_value]
                else:
                    range_values.append(axis_config.range.max_value)
            if len(range_values) == 2:
                config['range'] = range_values
                config['autorange'] = False

        # 刻度标签配置
        if not axis_config.show_tick_labels:
            config['showticklabels'] = False
        if axis_config.tick_angle is not None:
            config['tickangle'] = axis_config.tick_angle
        if axis_config.tick_prefix:
            config['tickprefix'] = axis_config.tick_prefix
        if axis_config.tick_suffix:
            config['ticksuffix'] = axis_config.tick_suffix
        if axis_config.tick_format:
            config['tickformat'] = axis_config.tick_format

        return config

    def _get_legend_config(self) -> Dict[str, Any]:
        """Get legend configuration, adjusting position based on colorbar presence."""
        legend_config = {
            'font': {'size': self.config.style.font.get("size_ticks", 10)},
            'bgcolor': 'rgba(255,255,255,0.8)'
        }

        # Adjust legend position based on colorbar configuration
        if self.config.colorbar.visible:
            if self.config.colorbar.orientation == 'vertical':
                # Colorbar is vertical (right side), move legend to top-left inside plot
                legend_config.update({
                    'x': 0.02,
                    'y': 0.98,
                    'xanchor': 'left',
                    'yanchor': 'top',
                    'bordercolor': '#cccccc',
                    'borderwidth': 1
                })
            else:
                # Colorbar is horizontal (bottom), place legend on right side
                legend_config.update({
                    'x': 1.02,
                    'y': 1,
                    'xanchor': 'left',
                    'yanchor': 'top'
                })
        else:
            # No colorbar, use default right position
            legend_config.update({
                'x': 1.02,
                'y': 1,
                'xanchor': 'left',
                'yanchor': 'top'
            })

        return legend_config

    def _convert_scale_type(self, scale_type: ScaleType) -> str:
        """Convert ScaleType enum to Plotly-compatible string."""
        # 直接返回枚举值，因为现在枚举值就是Plotly兼容的
        return scale_type.value

    def _apply_global_axis_styling(self, fig: go.Figure) -> None:
        """Apply global styling to all axes."""
        # Apply default styling to all axes
        tick_color = self.config.style.frame.get("color", "#666666")
        tick_width = self.config.style.frame.get("width", 2.0)

        fig.update_xaxes(
            showline=True,
            linewidth=tick_width,
            linecolor=tick_color,
            ticks='outside',
            tickcolor=tick_color,
            tickwidth=tick_width,
            ticklen=6,  # Standard tick length for main plot
            mirror=True
        )
        fig.update_yaxes(
            showline=True,
            linewidth=tick_width,
            linecolor=tick_color,
            ticks='outside',
            tickcolor=tick_color,
            tickwidth=tick_width,
            ticklen=6,
            mirror=True
        )

        # Apply specific styling to marginal plot axes
        # 边缘图设计原则：
        # - 数据轴（与主图共享）：不显示刻度线
        # - 计数轴（独立的频率/密度轴）：显示刻度线，位置在外侧
        has_marginal_x = self.config.marginal.enabled in [MarginalEnabled.X, MarginalEnabled.BOTH]
        has_marginal_y = self.config.marginal.enabled in [MarginalEnabled.Y, MarginalEnabled.BOTH]

        # Get tick styling from main plot configuration
        tick_color = self.config.style.frame.get("color", "#666666")
        tick_width = self.config.style.frame.get("width", 2.0)

        if has_marginal_x:
            # X marginal plot is always at row=1
            # X轴（数据轴，与主图共享）：不显示刻度线
            fig.update_xaxes(
                ticks='',  # 不显示刻度线
                row=1
            )
            # Y轴（计数轴）：显示刻度线，在外侧
            fig.update_yaxes(
                ticks='outside',
                tickcolor=tick_color,
                tickwidth=tick_width,
                ticklen=4,
                row=1
            )

        if has_marginal_y:
            # Y marginal plot position depends on layout
            if has_marginal_x:
                # 2x2 layout: Y marginal at row=2, col=2
                # X轴（计数轴）：显示刻度线，在外侧
                fig.update_xaxes(
                    ticks='outside',
                    tickcolor=tick_color,
                    tickwidth=tick_width,
                    ticklen=4,
                    row=2, col=2
                )
                # Y轴（数据轴，与主图共享）：不显示刻度线
                fig.update_yaxes(
                    ticks='',  # 不显示刻度线
                    row=2, col=2
                )
            else:
                # 1x2 layout: Y marginal at row=1, col=2
                # X轴（计数轴）：显示刻度线，在外侧
                fig.update_xaxes(
                    ticks='outside',
                    tickcolor=tick_color,
                    tickwidth=tick_width,
                    ticklen=4,
                    row=1, col=2
                )
                # Y轴（数据轴，与主图共享）：不显示刻度线
                fig.update_yaxes(
                    ticks='',  # 不显示刻度线
                    row=1, col=2
                )



    def _sync_marginal_axes(self, fig: go.Figure, has_marginal_x: bool, has_marginal_y: bool) -> None:
        """Ensure marginal axes match main axes for logarithmic scales.

        For Plotly 6.0+, Box, Violin, and KDE plots work natively with logarithmic scales
        when the axis type is set to 'log'. Statistical computations (quartiles, KDE)
        are performed in the original data space, while the display is transformed.
        """
        if has_marginal_x and self.config.xaxis.scale == ScaleType.LOG:
            scale_type = self._convert_scale_type(self.config.xaxis.scale)
            # Apply log scale to X marginal plot's X-axis (data axis)
            fig.update_xaxes(type=scale_type, row=1, col=1)

            # Also apply to main row if we have both marginals (2x2 layout)
            if has_marginal_y:
                fig.update_xaxes(type=scale_type, row=1, col=2)

        if has_marginal_y and self.config.yaxis.scale == ScaleType.LOG:
            y_col = 2
            y_row = 2 if has_marginal_x else 1
            scale_type = self._convert_scale_type(self.config.yaxis.scale)
            # Apply log scale to Y marginal plot's Y-axis (data axis)
            fig.update_yaxes(type=scale_type, row=y_row, col=y_col)

            # Also apply to main column if we have both marginals (2x2 layout)
            if has_marginal_x:
                fig.update_yaxes(type=scale_type, row=2, col=1)

    def _add_reference_line(self, fig: go.Figure, data_dict: Dict[str, pd.DataFrame],
                            selectors: CrossPlotColumnSelectors) -> None:
        """Add reference line to the main plot based on slope and intercept."""
        # Parse reference line configuration
        ref_config = self._get_reference_line_config()
        if not ref_config.visible:
            return

        has_marginal_x = self.config.marginal.enabled in [MarginalEnabled.X, MarginalEnabled.BOTH]
        has_marginal_y = self.config.marginal.enabled in [MarginalEnabled.Y, MarginalEnabled.BOTH]
        main_row, main_col = self._get_main_subplot_position(has_marginal_x, has_marginal_y)

        # Calculate data range for reference line
        all_x_series = []
        for s in selectors.series:
            df = data_dict.get(s.bundle_name)
            if df is not None and not df.empty and s.x_col and s.x_col in df.columns:
                all_x_series.append(df[s.x_col])

        if all_x_series:
            all_x = pd.concat(all_x_series).dropna()
            if all_x.empty:
                return

            # Get X axis range
            x_min, x_max = all_x.min(), all_x.max()
            x_range = x_max - x_min
            if x_range == 0: x_range = 1 # Avoid division by zero if all points are same
            x_min -= x_range * 0.05  # Extend slightly
            x_max += x_range * 0.05

            # Calculate Y values using y = slope * x + intercept
            y_start = ref_config.slope * x_min + ref_config.intercept
            y_end = ref_config.slope * x_max + ref_config.intercept

            # Determine line color and width
            line_color = ref_config.color or self.config.style.frame.get("color", "#666666")
            line_width = ref_config.width or self.config.style.linewidth.get("reference_line", 1.0)

            fig.add_trace(go.Scatter(
                x=[x_min, x_max],
                y=[y_start, y_end],
                mode='lines',
                line=dict(
                    color=line_color,
                    width=line_width,
                    dash=ref_config.style
                ),
                name=ref_config.name,
                showlegend=ref_config.show_legend,
                hoverinfo='skip'
            ), row=main_row, col=main_col)

    def _get_reference_line_config(self) -> ReferenceLineConfig:
        """Get reference line configuration, handling backward compatibility."""
        if isinstance(self.config.reference_line, bool):
            # Backward compatibility: convert bool to ReferenceLineConfig
            return ReferenceLineConfig(visible=self.config.reference_line)
        elif isinstance(self.config.reference_line, ReferenceLineConfig):
            return self.config.reference_line
        else:
            return ReferenceLineConfig(visible=False)
